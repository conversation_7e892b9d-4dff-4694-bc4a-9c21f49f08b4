
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core", "Core.csproj", "{50975b72-2f4d-2474-ec99-d7769b2db73d}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Framework", "Framework.csproj", "{c987017d-340f-ea9f-9f27-4f976aafb139}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UI", "UI.csproj", "{ef6db534-99d2-87d2-ee27-cb443ee4c9ad}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Core.Editor", "Core.Editor.csproj", "{d751c46c-8261-5c7f-ea9c-785e40c41cb7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Galaxy.MapleLeaf.Core.PlayerSystems", "Galaxy.MapleLeaf.Core.PlayerSystems.csproj", "{6c6250a3-c6b8-e513-6aff-5d562a165286}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tests", "Tests.csproj", "{d8374721-ff55-9d02-bc70-457afae0b720}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{e6bdc332-25e1-2831-82c1-25d0441455a3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Gameplay", "Gameplay.csproj", "{5042a3d8-c116-52f5-42d1-fd630e44ef5d}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{50975b72-2f4d-2474-ec99-d7769b2db73d}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50975b72-2f4d-2474-ec99-d7769b2db73d}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{c987017d-340f-ea9f-9f27-4f976aafb139}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{c987017d-340f-ea9f-9f27-4f976aafb139}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ef6db534-99d2-87d2-ee27-cb443ee4c9ad}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ef6db534-99d2-87d2-ee27-cb443ee4c9ad}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{d751c46c-8261-5c7f-ea9c-785e40c41cb7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{d751c46c-8261-5c7f-ea9c-785e40c41cb7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6c6250a3-c6b8-e513-6aff-5d562a165286}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6c6250a3-c6b8-e513-6aff-5d562a165286}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{d8374721-ff55-9d02-bc70-457afae0b720}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{d8374721-ff55-9d02-bc70-457afae0b720}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{e6bdc332-25e1-2831-82c1-25d0441455a3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{e6bdc332-25e1-2831-82c1-25d0441455a3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5042a3d8-c116-52f5-42d1-fd630e44ef5d}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5042a3d8-c116-52f5-42d1-fd630e44ef5d}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal

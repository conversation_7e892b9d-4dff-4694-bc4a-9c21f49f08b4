using NUnit.Framework;
using Unity.Entities;
using Unity.Mathematics;
using MyGame.Core;
using MyGame.Core.Systems;
using UnityEngine;
using Unity.Collections;

public class MapGenerationTests
{
    private World m_PreviousWorld;
    protected World World;
    protected EntityManager EntityManager;

    [SetUp]
    public virtual void Setup()
    {
        m_PreviousWorld = World.DefaultGameObjectInjectionWorld;
        // 使用 DefaultWorldInitialization 来创建和初始化所有默认及游戏定义的系统
        // 这将自动运行 GameBootstrapperSystem，并创建一个初始的生成请求
        DefaultWorldInitialization.Initialize("Test World", false);
        World = World.DefaultGameObjectInjectionWorld;
        EntityManager = World.EntityManager;
    }

    [TearDown]
    public virtual void TearDown()
    {
        if (World != null && World.IsCreated)
        {
            var allEntities = EntityManager.GetAllEntities(Allocator.Temp);
            if (allEntities.Length > 0)
            {
                EntityManager.DestroyEntity(allEntities);
            }
            allEntities.Dispose();
            
            World.Dispose();
            World = null;

            World.DefaultGameObjectInjectionWorld = m_PreviousWorld;
            m_PreviousWorld = null;
        }
    }

    // TODO: This test needs to be rewritten to support the new async job architecture.
    // The old test checked for the addition/enabling of GenerateWorldRequest, which no longer exists.
    // A new test should:
    // 1. Create a RequestTerrainPhysics entity.
    // 2. Update the world for several frames.
    // 3. Query for the JobTrackerComponent and assert its status eventually becomes 'Completed'.
    // 4. Alternatively, check for the creation of the 'MapGenerationCompleted' event entity.
    // [Test]
    // public void MapGeneration_RequestIsTransformedAndConsumed()
    // {
    //     // ...
    // }

    // TODO: This test is also obsolete as the tag-based completion system was replaced by an event-based one.
    // A new test should verify that after the MapGenerationCompleted event is fired,
    // the dependent systems (like physics and finalization) run correctly.
    // [Test]
    // public void MapGeneration_CompletionTags_AreAdded_AfterSystemUpdate()
    // {
    //     // ...
    // }
        
                
        
            [Test]
            public void MapGeneration_ValidateGeneratedData_IsValid()
    {
        // 1. 运行完整的生成流程
        World.Update(); // Bootstrapper
        World.Update(); // MapGeneration
        EntityManager.CompleteAllTrackedJobs();
        World.Update(); // Finalize jobs

        // 2. 查找地图实体并获取其数据
        var singletonQuery = EntityManager.CreateEntityQuery(typeof(WorldMapSingleton));
        var singleton = singletonQuery.GetSingleton<WorldMapSingleton>();
        var mapEntity = singleton.MapEntity;
        var mapComponent = EntityManager.GetComponentData<MapComponent>(mapEntity);
        int expectedWidth = mapComponent.Width;
        int expectedHeight = mapComponent.Height;
        
        // 4. 验证统计数据
        Assert.IsTrue(EntityManager.HasComponent<MapGenerationStats>(mapEntity), "MapGenerationStats component is missing.");
        var stats = EntityManager.GetComponentData<MapGenerationStats>(mapEntity);

        Assert.AreEqual(expectedWidth, stats.Width, "Stats width does not match request width.");
        Assert.AreEqual(expectedHeight, stats.Height, "Stats height does not match request height.");
        Assert.AreEqual(expectedWidth * expectedHeight, stats.TotalTiles, "TotalTiles in stats is incorrect.");
        
        int countedTiles = stats.FloorTiles + stats.WaterTiles + stats.MountainTiles;
        // Note: Cave entrances might replace other tiles, so the sum might not be a perfect match, 
        // but it should be reasonably close to the total.
        Assert.IsTrue(countedTiles <= stats.TotalTiles, "The sum of tile types exceeds the total number of tiles.");
        Assert.IsTrue(stats.GenerationComplete, "GenerationComplete flag in stats is false.");
        Assert.IsTrue(stats.ValidationCompleted, "ValidationCompleted flag in stats is false.");

        // 5. 验证树木数据
        Assert.IsTrue(EntityManager.HasBuffer<TreePosition>(mapEntity), "TreePosition buffer is missing.");
        var treeBuffer = EntityManager.GetBuffer<TreePosition>(mapEntity);

        foreach (var tree in treeBuffer)
        {
            Assert.IsTrue(tree.Value.x >= 0 && tree.Value.x < expectedWidth, $"Tree position X ({tree.Value.x}) is out of bounds.");
            Assert.IsTrue(tree.Value.y >= 0 && tree.Value.y < expectedHeight, $"Tree position Y ({tree.Value.y}) is out of bounds.");
        }
    }
}
using UnityEngine;

namespace MyProject.UI.Controllers
{
    /// <summary>
    /// DEPRECATED: Handles player input for UI and game actions, like pausing.
    /// This functionality has been moved to UIEventSystem.cs in the Core ECS systems.
    /// This file can be safely deleted after verifying the new system works.
    /// </summary>
    public class InputManager : MonoBehaviour
    {
        private bool _isPaused = false;

        void Update()
        {
            // Check for the Escape key to toggle the pause menu
            // if (Input.GetKeyDown(KeyCode.Escape))
            // {
            //     TogglePause();
            // }
        }

        /// <summary>
        /// Toggles the game's paused state.
        /// </summary>
        public void TogglePause()
        {
            _isPaused = !_isPaused;

            if (_isPaused)
            {
                PauseGame();
            }
            else
            {
                ResumeGame();
            }
        }

        private void PauseGame()
        {
            Time.timeScale = 0f; // Pause the game simulation
            // Show the pause menu
            if (UIManager.Instance != null)
            {
                UIManager.Instance.ShowView<UITK_PauseMenuView>();
            }
        }

        private void ResumeGame()
        {
            Time.timeScale = 1f; // Resume the game simulation
            // Hide the pause menu
            if (UIManager.Instance != null)
            {
                UIManager.Instance.HideView<UITK_PauseMenuView>();
            }
        }
    }
}
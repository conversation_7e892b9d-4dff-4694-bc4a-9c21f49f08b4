using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using MyProject.UI.Controllers;

/// <summary>
/// UI管理器 (UI Toolkit Version)
/// 负责管理所有UI Toolkit视图的生命周期。
/// </summary>
public class UIManager : MonoBehaviour
{
    private static UIManager _instance;
    public static UIManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindFirstObjectByType<UIManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("UIManager");
                    _instance = go.AddComponent<UIManager>();
                }
            }
            return _instance;
        }
    }

    // 缓存所有活动的UI Toolkit视图
    private readonly Dictionary<string, UITK_BaseView> _activeViews = new Dictionary<string, UITK_BaseView>();
    // 缓存UI Toolkit视图的Prefab
    private readonly Dictionary<string, GameObject> _viewPrefabs = new Dictionary<string, GameObject>();

    void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
        DontDestroyOnLoad(gameObject);

        // Ensure InputManager is attached to the same GameObject
        if (GetComponent<InputManager>() == null)
        {
            gameObject.AddComponent<InputManager>();
        }
    }

    void Start()
    {
        // 游戏启动时，默认显示核心UI
        ShowView<UITK_MainMenuView>();
        ShowView<UITK_HUDView>();
    }

    /// <summary>
    /// 显示视图。如果视图已存在，则激活它；否则创建它。
    /// </summary>
    public T ShowView<T>(object data = null) where T : UITK_BaseView
    {
        string viewName = typeof(T).Name;
        if (_activeViews.TryGetValue(viewName, out UITK_BaseView view))
        {
            view.Show(data);
            return view as T;
        }
        
        T newView = GetOrCreateView<T>();
        if (newView != null)
        {
            _activeViews[viewName] = newView;
            newView.Show(data);
            return newView;
        }
        return null;
    }

    /// <summary>
    /// 隐藏视图。
    /// </summary>
    public void HideView<T>() where T : UITK_BaseView
    {
        string viewName = typeof(T).Name;
        if (_activeViews.TryGetValue(viewName, out UITK_BaseView view))
        {
            view.Hide();
        }
    }

    /// <summary>
    /// 获取或创建视图实例。
    /// </summary>
    private T GetOrCreateView<T>() where T : UITK_BaseView
    {
        string viewName = typeof(T).Name;

        // 尝试在场景中查找已存在的实例 (虽然通常不推荐)
        T viewInScene = FindFirstObjectByType<T>();
        if (viewInScene != null && !viewInScene.gameObject.name.Contains("(Clone)"))
        {
            return viewInScene;
        }
        
        GameObject prefab = GetViewPrefab(viewName);
        if (prefab != null)
        {
            GameObject viewObject = Instantiate(prefab, transform);
            viewObject.name = viewName;
            return viewObject.GetComponent<T>();
        }
        
        Debug.LogError($"无法创建视图 '{viewName}'。资源不存在。");
        return null;
    }

    private GameObject GetViewPrefab(string viewName)
    {
        if (_viewPrefabs.TryGetValue(viewName, out GameObject prefab))
        {
            return prefab;
        }

        var loadedPrefab = Resources.Load<GameObject>($"UIToolkit/Prefabs/{viewName}");
        if (loadedPrefab != null)
        {
            _viewPrefabs[viewName] = loadedPrefab;
            return loadedPrefab;
        }

        Debug.LogError($"无法在 'Resources/UIToolkit/Prefabs/' 目录下找到名为 '{viewName}' 的Prefab。");
        return null;
    }

    public void HideAllViews()
    {
        foreach (var view in _activeViews.Values.ToList())
        {
            view.Hide();
        }
        _activeViews.Clear();
    }
}
using System;
using UnityEngine;
using MyGame.Core;

/// <summary>
/// 一个MonoBehaviour单例，作为从UIEventSystem (DOTS) 到UI (MonoBehaviour) 的事件接收器。
/// 它在Update中轮询事件队列，然后通过标准的C#事件将数据分发给UI层的监听者。
/// </summary>
public class UIEventListener : MonoBehaviour
{
    /// <summary>
    /// 静态单例实例。
    /// </summary>
    public static UIEventListener Instance { get; private set; }

    /// <summary>
    /// 当玩家数据更新事件从Core层传来时触发。
    /// </summary>
    public event Action<UIEventData> OnPlayerDataUpdated;
    
    /// <summary>
    /// 当地图数据更新事件从Core层传来时触发。
    /// </summary>
    public event Action<UIEventData> OnMapDataUpdated;

    /// <summary>
    /// 当游戏状态更新事件从Core层传来时触发。
    /// </summary>
    public event Action<UIEventData> OnGameStateUpdated;

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
        }
        else
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
    }

    private void Update()
    {
        // 每一帧都检查事件发件箱队列
        while (UIEventOutbox.Events.Count > 0)
        {
            var bufferElement = UIEventOutbox.Events.Dequeue();

            // 根据事件类型，触发对应的C#事件
            switch (bufferElement.EventType)
            {
                case UIEventType.PlayerDataUpdated:
                    OnPlayerDataUpdated?.Invoke(bufferElement.EventData);
                    break;
                case UIEventType.MapDataUpdated:
                    OnMapDataUpdated?.Invoke(bufferElement.EventData);
                    break;
                case UIEventType.GameStateUpdated:
                    OnGameStateUpdated?.Invoke(bufferElement.EventData);
                    break;
            }
        }
    }
}


/// <summary>
/// UI数据桥接器，作为ECS世界和UI (MonoBehaviour)世界沟通的桥梁。
/// 这是一个单例，负责持有UI所需的数据模型。
/// 它通过监听UIEventListener来获取最新的数据。
/// </summary>
public class UIDataBridge : MonoBehaviour
{
    public static UIDataBridge Instance { get; private set; }

    public PlayerUIModel PlayerModel { get; private set; }
    public MapUIModel MapModel { get; private set; }
    public GameStateUIModel GameStateModel { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
        DontDestroyOnLoad(gameObject);

        // 初始化UI数据模型
        if (PlayerModel == null)
        {
            PlayerModel = new PlayerUIModel();
        }
        MapModel = new MapUIModel();
        GameStateModel = new GameStateUIModel();
    }

    private void OnEnable()
    {
        if (UIEventListener.Instance != null)
        {
            UIEventListener.Instance.OnPlayerDataUpdated += HandlePlayerDataUpdated;
            UIEventListener.Instance.OnMapDataUpdated += HandleMapDataUpdated;
            UIEventListener.Instance.OnGameStateUpdated += HandleGameStateUpdated;
        }
        else
        {
            Debug.LogError("UIEventListener.Instance is null. Make sure a UIEventListener component exists in the scene and its script execution order is set correctly.");
        }
    }

    private void OnDisable()
    {
        if (UIEventListener.Instance != null)
        {
            UIEventListener.Instance.OnPlayerDataUpdated -= HandlePlayerDataUpdated;
            UIEventListener.Instance.OnMapDataUpdated -= HandleMapDataUpdated;
            UIEventListener.Instance.OnGameStateUpdated -= HandleGameStateUpdated;
        }
    }

    private void HandlePlayerDataUpdated(UIEventData data)
    {
        // 将从ECS事件接收到的数据，转换并更新到UI模型中
        var playerData = new PlayerData
        {
            // PlayerId在UIEventData中没有，但对于UI模型可能不是必需的
            PlayerName = data.PlayerData_Name.ToString(),
            Level = data.PlayerData_Level,
            Experience = data.PlayerData_Experience,
            Health = data.PlayerData_CurrentHealth,
            MaxHealth = data.PlayerData_MaxHealth,
            Gold = data.PlayerData_Gold
        };

        PlayerModel.UpdateFromPlayerData(playerData);
        
        Debug.Log($"UI Updated via Event: Player Level {playerData.Level}");
    }

    private void HandleMapDataUpdated(UIEventData data)
    {
        var mapData = new MapData
        {
            MapName = data.MapData_MapName.ToString(),
            Width = data.MapData_Width,
            Height = data.MapData_Height
        };
        MapModel.UpdateFromMapData(mapData);
        Debug.Log($"UI Updated via Event: Map Name {mapData.MapName}");
    }

    private void HandleGameStateUpdated(UIEventData data)
    {
        var gameStateData = new GameStateData
        {
            Status = (GameStatus)data.GameState_Status,
            GameTime = data.GameState_GameTime
        };
        GameStateModel.UpdateFromGameStateData(gameStateData);
        Debug.Log($"UI Updated via Event: Game Status {gameStateData.Status}");
    }
}


// --- UI Data Models ---

// Map Data
public struct MapData
{
    public string MapName;
    public int Width;
    public int Height;
}

public class MapUIModel : UIModelBase
{
    private MapData _currentMap;
    public MapData CurrentMap
    {
        get => _currentMap;
        private set
        {
            _currentMap = value;
            NotifyDataChanged(); // Notify that the whole model has changed
            NotifyDataChanged(nameof(CurrentMap));
        }
    }

    public void UpdateFromMapData(MapData data)
    {
        CurrentMap = data;
    }
}

// Game State Data
public struct GameStateData
{
    public GameStatus Status;
    public float GameTime;
}

public class GameStateUIModel : UIModelBase
{
    private GameStateData _currentState;
    public GameStateData CurrentState
    {
        get => _currentState;
        private set
        {
            _currentState = value;
            NotifyDataChanged(); // Notify that the whole model has changed
            NotifyDataChanged(nameof(CurrentState));
        }
    }

    public void UpdateFromGameStateData(GameStateData data)
    {
        CurrentState = data;
    }
}
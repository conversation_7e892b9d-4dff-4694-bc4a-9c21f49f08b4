using UnityEngine;
using UnityEngine.UIElements;
using MyGame.Core; // For UIEventProxy

public class UITK_MainMenuView : UITK_BaseView
{
    private Button _startButton;
    private Button _settingsButton;
    private Button _quitButton;

    protected override void OnInitialize()
    {
        _startButton = _root.Q<Button>("start-game-button");
        _settingsButton = _root.Q<Button>("settings-button");
        _quitButton = _root.Q<Button>("quit-button");
    }
    
    protected override void RegisterBindings()
    {
        _dataBinder.BindButtonClick(_startButton, OnStartGameClicked);
        _dataBinder.BindButtonClick(_settingsButton, OnSettingsClicked);
        _dataBinder.BindButtonClick(_quitButton, OnQuitClicked);
    }

    private void OnStartGameClicked()
    {
        // 与旧视图逻辑保持一致，发送事件到ECS后端
        var eventData = new UIEventData { ButtonId = "MainMenu_StartGame" };
        UIEventProxy.SendEvent(UIEventType.ButtonPressed, eventData);
        
        // 通常开始游戏后会隐藏主菜单
        UIManager.Instance.HideView<UITK_MainMenuView>();
    }

    private void OnSettingsClicked()
    {
        // 打开新的UI Toolkit设置视图
        UIManager.Instance.ShowView<UITK_SettingsView>();
    }

    private void OnQuitClicked()
    {
        // 退出游戏
        Application.Quit();
#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
#endif
    }

    public override void Show(object data = null)
    {
        base.Show(data);
        // 主菜单显示时暂停游戏
        Time.timeScale = 0f;
    }

    public override void Hide()
    {
        base.Hide();
        // 主菜单隐藏时恢复游戏
        Time.timeScale = 1f;
    }
}
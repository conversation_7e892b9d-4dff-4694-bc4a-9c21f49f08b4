using UnityEngine.UIElements;

public class UITK_HUDView : UITK_BaseView
{
    // UI 元素引用
    private Label _healthLabel;
    private ProgressBar _healthBar;
    private Label _levelLabel;
    private Label _xpLabel;
    private Label _mapNameLabel;
    private Label _gameStateLabel;

    // 数据模型引用
    private PlayerUIModel _playerModel;
    private MapUIModel _mapModel;
    private GameStateUIModel _gameStateModel;

    protected override void OnInitialize()
    {
        // 获取 UI 元素引用
        _healthLabel = _root.Q<Label>("health-label");
        _healthBar = _root.Q<ProgressBar>("health-bar");
        _levelLabel = _root.Q<Label>("level-label");
        _xpLabel = _root.Q<Label>("xp-label");
        _mapNameLabel = _root.Q<Label>("map-name-label");
        _gameStateLabel = _root.Q<Label>("game-state-label");

        // 获取模型引用
        var bridge = UIDataBridge.Instance;
        if (bridge != null)
        {
            _playerModel = bridge.PlayerModel;
            _mapModel = bridge.MapModel;
            _gameStateModel = bridge.GameStateModel;
        }
    }

    protected override void RegisterBindings()
    {
        if (_playerModel != null)
        {
            // --- 玩家数据绑定 ---
            _dataBinder.Bind(_levelLabel, _playerModel, m => m.Level, val => $"Level: {val}");
            _dataBinder.Bind(_xpLabel, _playerModel, m => m.Experience, val => $"XP: {val}");
            _dataBinder.Bind(_healthLabel, _playerModel, m => m.CurrentHealth, val => $"HP: {val}/{_playerModel.MaxHealth}");
            _dataBinder.Bind(_healthLabel, _playerModel, m => m.MaxHealth, val => $"HP: {_playerModel.CurrentHealth}/{val}");
        }

        if (_mapModel != null)
        {
            // --- 地图数据绑定 ---
            _dataBinder.Bind(_mapNameLabel, _mapModel, m => m.CurrentMap, val => $"Map: {val.MapName}");
        }

        if (_gameStateModel != null)
        {
            // --- 游戏状态绑定 ---
            _dataBinder.Bind(_gameStateLabel, _gameStateModel, m => m.CurrentState, val => $"Status: {val.Status}");
        }
    }
}
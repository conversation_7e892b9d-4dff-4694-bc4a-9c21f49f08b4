using UnityEngine.UIElements;
using System.Collections.Generic;
using MyProject.UI;

public class UITK_SettingsView : UITK_BaseView
{
    // 数据模型引用
    private PlayerUIModel _model;
    
    // UI 元素引用
    private Slider _musicVolumeSlider;
    private Slider _soundVolumeSlider;
    private Toggle _fullscreenToggle;
    private DropdownField _qualityDropdown;
    private Button _backButton;
    
    protected override void OnInitialize()
    {
        // 获取 UI 元素的引用
        _musicVolumeSlider = _root.Q<Slider>("music-volume-slider");
        _soundVolumeSlider = _root.Q<Slider>("sound-volume-slider");
        _fullscreenToggle = _root.Q<Toggle>("fullscreen-toggle");
        _qualityDropdown = _root.Q<DropdownField>("quality-dropdown");
        _backButton = _root.Q<Button>("back-button");
    }

    public override void Show(object data = null)
    {
        base.Show(data);
        if (data is PlayerUIModel model)
        {
            _model = model;
        }
    }

    protected override void SetupBindings(List<BindingSet> bindingSets)
    {
        if (_model == null) return;

        var settingsBindings = new BindingSet();
        settingsBindings.Add(() => _dataBinder.BindTwoWay(_musicVolumeSlider, _model, m => m.MusicVolume));
        settingsBindings.Add(() => _dataBinder.BindTwoWay(_soundVolumeSlider, _model, m => m.SoundVolume));
        settingsBindings.Add(() => _dataBinder.BindTwoWay(_fullscreenToggle, _model, m => m.IsFullscreen));
        bindingSets.Add(settingsBindings);

        var eventBindings = new BindingSet();
        eventBindings.Add(() => _dataBinder.BindButtonClick(_backButton, () =>
        {
            // _model.ApplySettings();
            UIManager.Instance.HideView<UITK_SettingsView>();
        }));
        bindingSets.Add(eventBindings);
    }
}
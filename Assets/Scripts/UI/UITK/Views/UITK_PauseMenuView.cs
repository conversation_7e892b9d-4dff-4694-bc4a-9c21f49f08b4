using UnityEngine;
using UnityEngine.UIElements;
using MyGame.Core;

public class UITK_PauseMenuView : UITK_BaseView
{
    private Button _resumeButton;
    private Button _settingsButton;
    private Button _mainMenuButton;

    protected override void OnInitialize()
    {
        _resumeButton = _root.Q<Button>("resume-button");
        _settingsButton = _root.Q<Button>("settings-button");
        _mainMenuButton = _root.Q<Button>("main-menu-button");
    }
    
    protected override void RegisterBindings()
    {
        _dataBinder.BindButtonClick(_resumeButton, OnResumeClicked);
        _dataBinder.BindButtonClick(_settingsButton, OnSettingsClicked);
        _dataBinder.BindButtonClick(_mainMenuButton, OnMainMenuClicked);
    }

    private void OnResumeClicked()
    {
        var eventData = new UIEventData { ButtonId = "PauseMenu_Resume" };
        UIEventProxy.SendEvent(UIEventType.ButtonPressed, eventData);
        UIManager.Instance.HideView<UITK_PauseMenuView>();
    }

    private void OnSettingsClicked()
    {
        UIManager.Instance.ShowView<UITK_SettingsView>();
    }

    private void OnMainMenuClicked()
    {
        var eventData = new UIEventData { ButtonId = "PauseMenu_MainMenu" };
        UIEventProxy.SendEvent(UIEventType.ButtonPressed, eventData);
        // 通常返回主菜单也会隐藏暂停菜单
        UIManager.Instance.HideView<UITK_PauseMenuView>();
    }

    public override void Show(object data = null)
    {
        base.Show(data);
        // 暂停菜单显示时暂停游戏
        Time.timeScale = 0f;
    }

    public override void Hide()
    {
        base.Hide();
        // 暂停菜单隐藏时恢复游戏
        Time.timeScale = 1f;
    }
}
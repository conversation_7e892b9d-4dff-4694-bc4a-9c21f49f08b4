using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq.Expressions;
using UnityEngine.UIElements;

public class UITK_DataBinder
{
    private readonly List<Action> _cleanupActions = new List<Action>();

    public void Bind<TModel, TProperty>(
        Label element,
        TModel model,
        Expression<Func<TModel, TProperty>> propertyExpression,
        Func<TProperty, string> formatter = null)
        where TModel : INotifyPropertyChanged
    {
        if (element == null || model == null || propertyExpression == null) return;

        var memberExpression = propertyExpression.Body as MemberExpression;
        if (memberExpression == null)
        {
            throw new ArgumentException("Expression must be a member expression");
        }
        string propertyName = memberExpression.Member.Name;
        var propertyFunc = propertyExpression.Compile();

        Action updateAction = () =>
        {
            var value = propertyFunc(model);
            element.text = formatter != null ? formatter(value) : value?.ToString() ?? "";
        };

        PropertyChangedEventHandler handler = (sender, args) =>
        {
            if (string.IsNullOrEmpty(args.PropertyName) || args.PropertyName == propertyName)
            {
                updateAction();
            }
        };

        model.PropertyChanged += handler;
        _cleanupActions.Add(() => model.PropertyChanged -= handler);

        updateAction();
    }

    public void BindTwoWay<TModel, TProperty>(
        INotifyValueChanged<TProperty> element,
        TModel model,
        Expression<Func<TModel, TProperty>> propertyExpression)
        where TModel : class, INotifyPropertyChanged
    {
        if (element == null || model == null || propertyExpression == null) return;

        var memberExpression = propertyExpression.Body as MemberExpression;
        if (memberExpression == null)
        {
            throw new ArgumentException("Expression must be a member expression");
        }
        string propertyName = memberExpression.Member.Name;
        var propertyFunc = propertyExpression.Compile();
        var propertySetter = CreateSetter(propertyExpression);
        
        bool isUpdating = false;

        Action<TProperty> updateModel = (value) =>
        {
            if (isUpdating) return;
            isUpdating = true;
            propertySetter(model, value);
            isUpdating = false;
        };
        
        EventCallback<ChangeEvent<TProperty>> viewCallback = evt => updateModel(evt.newValue);
        element.RegisterValueChangedCallback(viewCallback);

        Action updateView = () =>
        {
            if (isUpdating) return;
            isUpdating = true;
            element.SetValueWithoutNotify(propertyFunc(model));
            isUpdating = false;
        };

        PropertyChangedEventHandler modelHandler = (sender, args) =>
        {
            if (string.IsNullOrEmpty(args.PropertyName) || args.PropertyName == propertyName)
            {
                updateView();
            }
        };

        model.PropertyChanged += modelHandler;
        _cleanupActions.Add(() =>
        {
            element.UnregisterValueChangedCallback(viewCallback);
            model.PropertyChanged -= modelHandler;
        });

        updateView();
    }
    
    public void BindButtonClick(Button button, Action action)
    {
        if (button == null) return;
        button.clicked += action;
        _cleanupActions.Add(() => button.clicked -= action);
    }
    
    private static Action<T, TValue> CreateSetter<T, TValue>(Expression<Func<T, TValue>> getter)
    {
        var member = (MemberExpression)getter.Body;
        var param = Expression.Parameter(typeof(TValue), "value");
        var setter = Expression.Lambda<Action<T, TValue>>(
            Expression.Assign(member, param),
            getter.Parameters[0],
            param
        );
        return setter.Compile();
    }

    public void UnbindAll()
    {
        foreach (var cleanup in _cleanupActions)
        {
            cleanup();
        }
        _cleanupActions.Clear();
    }
}
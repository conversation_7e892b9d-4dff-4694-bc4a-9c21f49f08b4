using UnityEngine;
using UnityEngine.UIElements;
using System;
using System.Collections.Generic;
using MyProject.UI;

/// <summary>
/// A helper class to group data binding definitions for better organization.
/// </summary>
public class BindingSet
{
    private readonly List<Action> _bindingActions = new List<Action>();

    public void Add(Action bindingAction)
    {
        _bindingActions.Add(bindingAction);
    }

    public void Apply()
    {
        foreach (var action in _bindingActions)
        {
            action?.Invoke();
        }
    }
}        


/// <summary>
/// UI Toolkit 视图的基类。
/// 负责处理 UIDocument 的初始化、元素的查询以及通用的显示/隐藏逻辑。
/// </summary>
[RequireComponent(typeof(UIDocument))]
public abstract class UITK_BaseView : MonoBehaviour
{
    [Header("UI Toolkit 设置")]
    [Tooltip("视图的唯一名称")]
    public string ViewName;

    [Tooltip("如果为 true，视图将在启动时自动隐藏")]
    public bool AutoHideOnStart = false;

    [Tooltip("视图的根VisualElement")]
    protected VisualElement _root;

    [Tooltip("用于处理数据绑定的辅助类")]
    protected UITK_DataBinder _dataBinder;
    
    [Tooltip("与此视图关联的UIDocument组件")]
    protected UIDocument _document;

    /// <summary>
    /// 视图是否可见。
    /// </summary>
    public bool IsVisible { get; private set; }

    protected virtual void Awake()
    {
        _document = GetComponent<UIDocument>();
        if (_document == null)
        {
            Debug.LogError($"{gameObject.name} 上缺少 UIDocument 组件!");
            return;
        }
        _root = _document.rootVisualElement;
        
        _dataBinder = new UITK_DataBinder();

        if (AutoHideOnStart)
        {
            _root.style.display = DisplayStyle.None;
            IsVisible = false;
        }
        else
        {
            IsVisible = true;
        }
    }
    
    /// <summary>
    /// 在视图首次启用时调用，用于设置UI元素和注册事件。
    /// </summary>
    protected virtual void OnEnable()
    {
        OnInitialize();
        RegisterCallbacks();
        RegisterBindings();
    }

    /// <summary>
    /// 在视图禁用时调用，用于注销事件，防止内存泄漏。
    /// </summary>
    protected virtual void OnDisable()
    {
        UnregisterCallbacks();
        UnregisterBindings();
    }

    /// <summary>
    /// 初始化视图，查询必要的UI元素。应在子类中重写。
    /// </summary>
    protected virtual void OnInitialize() { }

    /// <summary>
    /// 注册UI元素的事件回调。应在子类中重写。
    /// </summary>
    protected virtual void RegisterCallbacks() { }

    /// <summary>
    /// 注销UI元素的事件回调。应在子类中重写。
    /// </summary>
    protected virtual void UnregisterCallbacks() { }
    
    /// <summary>
    /// New method for subclasses to define their binding sets.
    /// </summary>
    protected virtual void SetupBindings(List<BindingSet> bindingSets) { }

    /// <summary>
    /// 注册数据模型到UI元素的绑定。
    /// This is now a central place to apply all binding sets.
    /// </summary>
    protected virtual void RegisterBindings()
    {
        var bindingSets = new List<BindingSet>();
        SetupBindings(bindingSets);

        foreach (var set in bindingSets)
        {
            set.Apply();
        }
    }

    /// <summary>
    /// 注销数据绑定。应在子类中重写。
    /// </summary>
    protected virtual void UnregisterBindings()
    {
        _dataBinder?.UnbindAll();
    }


    /// <summary>
    /// 显示视图。
    /// </summary>
    /// <param name="data">可选的、传递给视图的数据。</param>
    public virtual void Show(object data = null)
    {
        if (IsVisible) return;
        
        IsVisible = true;
        _root.style.display = DisplayStyle.Flex;
        OnShow(data);
    }

    /// <summary>
    /// 隐藏视图。
    /// </summary>
    public virtual void Hide()
    {
        if (!IsVisible) return;

        IsVisible = false;
        _root.style.display = DisplayStyle.None;
        OnHide();
    }

    /// <summary>
    /// 当视图显示时调用的生命周期方法。
    /// </summary>
    protected virtual void OnShow(object data) { }
    
    /// <summary>
    /// 当视图隐藏时调用的生命周期方法。
    /// </summary>
    protected virtual void OnHide() { }
}
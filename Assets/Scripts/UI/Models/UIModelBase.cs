using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// 背包UI数据模型
/// </summary>
[System.Serializable]
public class InventoryUIModel : UIModelBase
{
    [Header("背包设置")]
    public int MaxSlots = 30;
    public List<InventoryItem> Items = new List<InventoryItem>();

    // 计算属性
    public int UsedSlots => Items.Count;
    public int FreeSlots => MaxSlots - UsedSlots;
    public bool IsFull => UsedSlots >= MaxSlots;

    protected override void InitializeModel()
    {
        base.InitializeModel();
        ModelId = "InventoryUI";
    }

    /// <summary>
    /// 添加物品
    /// </summary>
    public bool AddItem(string itemId, string itemName, Sprite itemIcon, int quantity = 1, System.Object data = null)
    {
        if (IsFull)
        {
            // EventManager.Instance.TriggerEvent(GameEvents.InventoryFull);
            return false;
        }

        // 检查是否已存在相同物品（可堆叠）
        var existingItem = Items.Find(item => item.ItemId == itemId && item.Stackable);
        if (existingItem != null)
        {
            existingItem.Quantity += quantity;
        }
        else
        {
            // 添加新物品
            var newItem = new InventoryItem
            {
                ItemId = itemId,
                ItemName = itemName,
                Icon = itemIcon,
                Quantity = quantity,
                Stackable = true,
                Data = data
            };
            Items.Add(newItem);
        }

        NotifyDataChanged("Items");
        Debug.Log($"Added {itemName} x{quantity} to inventory");

        return true;
    }

    /// <summary>
    /// 移除物品
    /// </summary>
    public bool RemoveItem(string itemId, int quantity = 1)
    {
        var item = Items.Find(i => i.ItemId == itemId);
        if (item != null)
        {
            item.Quantity -= quantity;

            if (item.Quantity <= 0)
            {
                Items.Remove(item);

                // 发送物品完全移除事件
                // EventManager.Instance.TriggerEvent(GameEvents.ItemUsed,
                //     new EventData.ItemData { ItemId = itemId, ItemName = item.ItemName, Quantity = 0 });
            }

            NotifyDataChanged("Items");
            Debug.Log($"Removed {quantity} of {item.ItemName} from inventory");

            return true;
        }

        return false;
    }

    /// <summary>
    /// 使用物品
    /// </summary>
    public bool UseItem(string itemId, InventoryItem itemUsed)
    {
        var item = Items.Find(i => i.ItemId == itemId);
        if (item != null)
        {
            if (item.Usable)
            {
                item.Quantity--;

                if (item.Quantity <= 0)
                {
                    Items.Remove(item);
                }

                NotifyDataChanged("Items");

                // 发送物品使用事件
                // EventManager.Instance.TriggerEvent(GameEvents.ItemUsed,
                //     new EventData.ItemData
                //     {
                //         ItemId = itemId,
                //         ItemName = item.ItemName,
                //         Quantity = 1
                //     });

                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 获取物品
    /// </summary>
    public InventoryItem GetItem(string itemId)
    {
        return Items.Find(item => item.ItemId == itemId);
    }

    /// <summary>
    /// 检查是否有足够数量的物品
    /// </summary>
    public bool HasEnoughItems(string itemId, int requiredQuantity)
    {
        var item = GetItem(itemId);
        return item != null && item.Quantity >= requiredQuantity;
    }

    /// <summary>
    /// 清空背包
    /// </summary>
    public void ClearInventory()
    {
        Items.Clear();
        NotifyDataChanged("Items");

        Debug.Log("Inventory cleared");
    }

    /// <summary>
    /// 整理背包（可扩展功能）
    /// </summary>
    public void OrganizeInventory()
    {
        // 按物品ID排序，堆叠相同物品等
        Items.Sort((a, b) => a.ItemId.CompareTo(b.ItemId));
        NotifyDataChanged("Items");
    }
}

/// <summary>
/// 背包物品数据结构
/// </summary>
[System.Serializable]
public class InventoryItem
{
    public string ItemId;
    public string ItemName;
    public Sprite Icon;
    public int Quantity;
    public bool Stackable = true;
    public bool Usable = true;
    public System.Object Data; // 额外的物品数据

    public string Description => $"{ItemName} x{Quantity}";
}

/// <summary>
/// 商店UI数据模型
/// </summary>
[System.Serializable]
public class ShopUIModel : UIModelBase
{
    [Header("商店数据")]
    public string ShopName;
    public string ShopDescription;
    public List<ShopItem> AvailableItems = new List<ShopItem>();
    public float BuyMultiplier = 1.0f;  // 买入优惠倍率
    public float SellMultiplier = 0.8f; // 卖出折扣倍率

    protected override void InitializeModel()
    {
        base.InitializeModel();
        ModelId = "ShopUI";
    }

    /// <summary>
    /// 添加商品
    /// </summary>
    public void AddItem(string itemId, string itemName, Sprite itemIcon, int basePrice, int stock = -1)
    {
        var newItem = new ShopItem
        {
            ItemId = itemId,
            ItemName = itemName,
            Icon = itemIcon,
            BasePrice = basePrice,
            CurrentStock = stock
        };

        AvailableItems.Add(newItem);
        NotifyDataChanged("Items");
    }

    /// <summary>
    /// 移除商品
    /// </summary>
    public void RemoveItem(string itemId)
    {
        var item = AvailableItems.Find(i => i.ItemId == itemId);
        if (item != null)
        {
            AvailableItems.Remove(item);
            NotifyDataChanged("Items");
        }
    }

    /// <summary>
    /// 计算买入价格
    /// </summary>
    public int GetBuyPrice(string itemId)
    {
        var item = AvailableItems.Find(i => i.ItemId == itemId);
        if (item != null)
        {
            return Mathf.RoundToInt(item.BasePrice * BuyMultiplier);
        }
        return 0;
    }

    /// <summary>
    /// 计算卖出价格
    /// </summary>
    public int GetSellPrice(string itemId)
    {
        var item = AvailableItems.Find(i => i.ItemId == itemId);
        if (item != null)
        {
            return Mathf.RoundToInt(item.BasePrice * SellMultiplier);
        }
        return 0;
    }

    /// <summary>
    /// 检查商品是否有货
    /// </summary>
    public bool IsItemInStock(string itemId, int quantity = 1)
    {
        var item = AvailableItems.Find(i => i.ItemId == itemId);
        if (item != null)
        {
            return item.CurrentStock == -1 || item.CurrentStock >= quantity;
        }
        return false;
    }

    /// <summary>
    /// 购买商品
    /// </summary>
    public bool BuyItem(string itemId, int quantity = 1)
    {
        var item = AvailableItems.Find(i => i.ItemId == itemId);
        if (item != null && IsItemInStock(itemId, quantity))
        {
            int totalCost = GetBuyPrice(itemId) * quantity;

            // 这里应该调用玩家的金币系统进行支付
            // 简化实现，假设支付成功

            // 减少库存（如果不是无限供应）
            if (item.CurrentStock > 0)
            {
                item.CurrentStock -= quantity;
            }

            // EventManager.Instance.TriggerEvent(GameEvents.ItemCollected,
            //     new EventData.ItemData
            //     {
            //         ItemId = itemId,
            //         ItemName = item.ItemName,
            //         Quantity = quantity
            //     });

            NotifyDataChanged("Items");
            return true;
        }

        return false;
    }

    /// <summary>
    /// 重置商店物品
    /// </summary>
    public void Restock()
    {
        foreach (var item in AvailableItems)
        {
            item.CurrentStock = item.MaxStock;
        }
        NotifyDataChanged("Items");
        Debug.Log($"{ShopName} restocked");
    }
}

/// <summary>
/// 商店商品数据结构
/// </summary>
[System.Serializable]
public class ShopItem
{
    public string ItemId;
    public string ItemName;
    public Sprite Icon;
    public int BasePrice;
    public int CurrentStock = -1; // -1表示无限库存
    public int MaxStock = -1;
    public string Description;

    public bool IsInfinite => CurrentStock == -1;
    public bool IsInStock => IsInfinite || CurrentStock > 0;
}

/// <summary>
/// 设置UI数据模型
/// </summary>
[System.Serializable]
public class SettingsUIModel : UIModelBase
{
    [Header("音量设置")]
    public float MusicVolume = 0.7f;
    public float SoundVolume = 0.8f;
    public float VoiceVolume = 1.0f;

    [Header("图形设置")]
    public bool IsFullscreen = true;
    public int ResolutionIndex = 0;
    public int QualityIndex = 2;

    [Header("游戏设置")]
    public bool EnableVibration = true;
    public bool EnableParticles = true;
    public bool ShowDamageNumbers = true;

    protected override void InitializeModel()
    {
        base.InitializeModel();
        ModelId = "SettingsUI";

        // 从保存的设置中加载数据
        // var saveManager = SaveLoadManager.Instance;
        // if (saveManager != null)
        // {
        //     var savedSettings = saveManager.GetGameSettings();
        //     LoadFromGameSettings(savedSettings);
        // }
    }

    /// <summary>
    /// 从游戏设置加载
    /// </summary>
    public void LoadFromGameSettings(GameSettings settings)
    {
        MusicVolume = settings.MusicVolume;
        SoundVolume = settings.SoundVolume;
        VoiceVolume = settings.VoiceVolume;
        IsFullscreen = settings.IsFullscreen;
        ResolutionIndex = settings.ResolutionIndex;
        QualityIndex = settings.QualityIndex;

        NotifyDataChanged("All");
    }

    /// <summary>
    /// 保存到游戏设置
    /// </summary>
    public GameSettings SaveToGameSettings()
    {
        return new GameSettings
        {
            MusicVolume = this.MusicVolume,
            SoundVolume = this.SoundVolume,
            VoiceVolume = this.VoiceVolume,
            IsFullscreen = this.IsFullscreen,
            ResolutionIndex = this.ResolutionIndex,
            QualityIndex = this.QualityIndex
        };
    }

    /// <summary>
    /// 应用设置到游戏
    /// </summary>
    public void ApplySettings()
    {
        // 应用音量设置
        // var audioService = AudioService.Instance;
        // if (audioService != null)
        // {
        //     audioService.SetVolume(MusicVolume, SoundVolume, VoiceVolume);
        // }

        // 应用图像设置
        Screen.fullScreen = IsFullscreen;

        // 设置质量等级
        QualitySettings.SetQualityLevel(QualityIndex);

        // 应用分辨率（简化处理）
        // 这里需要根据ResolutionIndex设置实际分辨率

        // 保存设置
        // var saveManager = SaveLoadManager.Instance;
        // if (saveManager != null)
        // {
        //     var gameSettings = SaveToGameSettings();
        //     saveManager.UpdateGameSettings(gameSettings);
        // }

        NotifyDataChanged("Applied");
        Debug.Log("Settings applied and saved");
    }

    /// <summary>
    /// 重置为默认设置
    /// </summary>
    public void ResetToDefaults()
    {
        MusicVolume = 0.7f;
        SoundVolume = 0.8f;
        VoiceVolume = 1.0f;
        IsFullscreen = true;
        ResolutionIndex = 0;
        QualityIndex = 2;

        NotifyDataChanged("All");

        // 立即应用
        ApplySettings();
    }

    /// <summary>
    /// 设置音乐音量
    /// </summary>
    public void SetMusicVolume(float volume)
    {
        MusicVolume = Mathf.Clamp01(volume);
        NotifyDataChanged("MusicVolume");
    }

    /// <summary>
    /// 设置音效音量
    /// </summary>
    public void SetSoundVolume(float volume)
    {
        SoundVolume = Mathf.Clamp01(volume);
        NotifyDataChanged("SoundVolume");
    }

    /// <summary>
    /// 设置语音音量
    /// </summary>
    public void SetVoiceVolume(float volume)
    {
        VoiceVolume = Mathf.Clamp01(volume);
        NotifyDataChanged("VoiceVolume");
    }

    /// <summary>
    /// 设置全屏模式
    /// </summary>
    public void SetFullscreen(bool fullscreen)
    {
        IsFullscreen = fullscreen;
        NotifyDataChanged("Fullscreen");
    }

    /// <summary>
    /// 设置质量等级
    /// </summary>
    public void SetQualityLevel(int qualityIndex)
    {
        QualityIndex = qualityIndex;
        NotifyDataChanged("Quality");
    }

    /// <summary>
    /// 设置分辨率
    /// </summary>
    public void SetResolution(int resolutionIndex)
    {
        ResolutionIndex = resolutionIndex;
        NotifyDataChanged("Resolution");
    }
}

/// <summary>
/// 游戏设置数据结构 (临时占位)
/// </summary>
[System.Serializable]
public class GameSettings
{
    public float MusicVolume = 0.7f;
    public float SoundVolume = 0.8f;
    public float VoiceVolume = 1.0f;
    public bool IsFullscreen = true;
    public int ResolutionIndex = 0;
    public int QualityIndex = 2;
}
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 游戏工具类集合
/// 提供常用的工具方法
/// </summary>
public static class GameUtils
{
    /// <summary>
    /// 相机相关工具
    /// </summary>
    public static class CameraUtils
    {
        /// <summary>
        /// 获取屏幕边界的世界坐标
        /// </summary>
        public static Bounds GetScreenBounds(Camera camera = null)
        {
            if (camera == null) camera = Camera.main;

            var screenAspect = (float)Screen.width / Screen.height;
            var cameraHeight = camera.orthographicSize * 2;
            var bounds = new Bounds(
                camera.transform.position,
                new Vector3(cameraHeight * screenAspect, cameraHeight, 0f)
            );

            return bounds;
        }

        /// <summary>
        /// 检查位置是否在屏幕内
        /// </summary>
        public static bool IsPositionOnScreen(Vector3 position, Camera camera = null)
        {
            if (camera == null) camera = Camera.main;

            Vector3 viewportPoint = camera.WorldToViewportPoint(position);
            return viewportPoint.x >= 0f && viewportPoint.x <= 1f &&
                   viewportPoint.y >= 0f && viewportPoint.y <= 1f;
        }

        /// <summary>
        /// 将屏幕坐标转换为世界坐标
        /// </summary>
        public static Vector3 ScreenToWorldPoint(Vector3 screenPoint, float distanceFromCamera = 10f, Camera camera = null)
        {
            if (camera == null) camera = Camera.main;

            Ray ray = camera.ScreenPointToRay(screenPoint);
            return ray.GetPoint(distanceFromCamera);
        }

        /// <summary>
        /// 平滑跟随目标
        /// </summary>
        public static IEnumerator SmoothFollow(Transform follower, Transform target, float speed, float maxDistance = float.MaxValue)
        {
            while (true)
            {
                Vector3 direction = target.position - follower.position;
                float distance = direction.magnitude;

                if (distance > 0.1f && distance <= maxDistance)
                {
                    Vector3 targetPosition = follower.position + direction.normalized * speed * Time.deltaTime;
                    follower.position = Vector3.Lerp(follower.position, targetPosition, speed * Time.deltaTime);
                }

                yield return null;
            }
        }
    }

    /// <summary>
    /// 数学工具
    /// </summary>
    public static class MathUtils
    {
        /// <summary>
        /// 映射值到新范围内
        /// </summary>
        public static float Map(float value, float fromMin, float fromMax, float toMin, float toMax)
        {
            return (value - fromMin) / (fromMax - fromMin) * (toMax - toMin) + toMin;
        }

        /// <summary>
        /// 计算两点间的距离（2D）
        /// </summary>
        public static float Distance2D(Vector2 a, Vector2 b)
        {
            return Vector2.Distance(a, b);
        }

        /// <summary>
        /// 计算两点间的距离（3D）
        /// </summary>
        public static float Distance3D(Vector3 a, Vector3 b)
        {
            return Vector3.Distance(a, b);
        }

        /// <summary>
        /// 计算角度
        /// </summary>
        public static float Angle(Vector2 from, Vector2 to)
        {
            return Vector2.SignedAngle(Vector2.right, to - from);
        }

        /// <summary>
        /// 计算向量长度
        /// </summary>
        public static float Magnitude(Vector2 vector)
        {
            return vector.magnitude;
        }

        public static float Magnitude(Vector3 vector)
        {
            return vector.magnitude;
        }

        /// <summary>
        /// 角度转弧度
        /// </summary>
        public static float DegreesToRadians(float degrees)
        {
            return degrees * Mathf.Deg2Rad;
        }

        /// <summary>
        /// 弧度转角度
        /// </summary>
        public static float RadiansToDegrees(float radians)
        {
            return radians * Mathf.Rad2Deg;
        }
    }

    /// <summary>
    /// 对象池工具
    /// </summary>
    public static class ObjectPoolUtils
    {
        private static Dictionary<string, Queue<GameObject>> _objectPools = new Dictionary<string, Queue<GameObject>>();

        /// <summary>
        /// 创建对象池
        /// </summary>
        public static void CreatePool(string poolName, GameObject prefab, int initialSize = 10, Transform parent = null)
        {
            if (_objectPools.ContainsKey(poolName))
            {
                Debug.LogWarning($"Object pool '{poolName}' already exists!");
                return;
            }

            _objectPools[poolName] = new Queue<GameObject>();

            for (int i = 0; i < initialSize; i++)
            {
                GameObject obj = Object.Instantiate(prefab, parent);
                obj.SetActive(false);
                _objectPools[poolName].Enqueue(obj);
            }

            Debug.Log($"Object pool '{poolName}' created with {initialSize} objects");
        }

        /// <summary>
        /// 从对象池获取对象
        /// </summary>
        public static GameObject GetObject(string poolName, Transform parent = null)
        {
            if (_objectPools.TryGetValue(poolName, out Queue<GameObject> pool))
            {
                if (pool.Count > 0)
                {
                    GameObject obj = pool.Dequeue();
                    obj.SetActive(true);
                    return obj;
                }
            }

            Debug.LogWarning($"No available objects in pool '{poolName}'");
            return null;
        }

        /// <summary>
        /// 将对象放回对象池
        /// </summary>
        public static void ReturnObject(string poolName, GameObject obj)
        {
            if (_objectPools.TryGetValue(poolName, out Queue<GameObject> pool))
            {
                obj.SetActive(false);
                pool.Enqueue(obj);
            }
            else
            {
                Debug.LogWarning($"Pool '{poolName}' does not exist!");
            }
        }

        /// <summary>
        /// 清空对象池
        /// </summary>
        public static void ClearPool(string poolName)
        {
            if (_objectPools.TryGetValue(poolName, out Queue<GameObject> pool))
            {
                while (pool.Count > 0)
                {
                    GameObject obj = pool.Dequeue();
                    Object.Destroy(obj);
                }

                _objectPools.Remove(poolName);
                Debug.Log($"Object pool '{poolName}' cleared");
            }
        }
    }

    /// <summary>
    /// UI工具
    /// </summary>
    public static class UIUtils
    {
        /// <summary>
        /// 渐变动画
        /// </summary>
        public static IEnumerator Fade(float startValue, float endValue, float duration, System.Action<float> onValueChanged)
        {
            float elapsedTime = 0f;
            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / duration;
                float currentValue = Mathf.Lerp(startValue, endValue, progress);
                onValueChanged(currentValue);
                yield return null;
            }

            // 确保最终值正确
            onValueChanged(endValue);
        }

        /// <summary>
        /// 延迟执行
        /// </summary>
        public static Coroutine Delay(float seconds, System.Action callback)
        {
            return CoroutineRunner.Instance.StartCoroutine(DelayCoroutine(seconds, callback));
        }

        private static IEnumerator DelayCoroutine(float seconds, System.Action callback)
        {
            yield return new WaitForSeconds(seconds);
            callback?.Invoke();
        }

        /// <summary>
        /// 协程运行器单例
        /// </summary>
        private class CoroutineRunner : MonoBehaviour
        {
            private static CoroutineRunner _instance;

            public static CoroutineRunner Instance
            {
                get
                {
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("CoroutineRunner");
                        _instance = go.AddComponent<CoroutineRunner>();
                        DontDestroyOnLoad(go);
                    }
                    return _instance;
                }
            }
        }

        /// <summary>
        /// 设置RectTransform位置
        /// </summary>
        public static void SetPosition(RectTransform rectTransform, Vector2 position)
        {
            rectTransform.anchoredPosition = position;
        }

        /// <summary>
        /// 设置RectTransform大小
        /// </summary>
        public static void SetSize(RectTransform rectTransform, Vector2 size)
        {
            rectTransform.sizeDelta = size;
        }

        /// <summary>
        /// 获取屏幕比例
        /// </summary>
        public static Vector2 GetScreenSize()
        {
            return new Vector2(Screen.width, Screen.height);
        }
    }

    /// <summary>
    /// 调试工具
    /// </summary>
    public static class DebugUtils
    {
        /// <summary>
        /// 在编辑器中画线
        /// </summary>
        public static void DrawLine(Vector3 start, Vector3 end, Color color = default, float duration = 0f)
        {
            if (color == default) color = Color.white;
            Debug.DrawLine(start, end, color, duration);
        }

        /// <summary>
        /// 画圆
        /// </summary>
        public static void DrawCircle(Vector3 center, float radius, Color color = default, float duration = 0f, int segments = 12)
        {
            if (color == default) color = Color.white;

            float angleStep = 360f / segments;
            Vector3 previousPoint = center + Quaternion.Euler(0, 0, 0) * (Vector3.right * radius);

            for (int i = 1; i <= segments; i++)
            {
                Vector3 currentPoint = center + Quaternion.Euler(0, 0, i * angleStep) * (Vector3.right * radius);
                Debug.DrawLine(previousPoint, currentPoint, color, duration);
                previousPoint = currentPoint;
            }
        }

        /// <summary>
        /// 画矩形
        /// </summary>
        public static void DrawRectangle(Vector3 center, Vector2 size, Color color = default, float duration = 0f)
        {
            if (color == default) color = Color.white;

            Vector3 halfSize = size / 2f;
            Vector3 topLeft = center + new Vector3(-halfSize.x, halfSize.y, 0);
            Vector3 topRight = center + new Vector3(halfSize.x, halfSize.y, 0);
            Vector3 bottomLeft = center + new Vector3(-halfSize.x, -halfSize.y, 0);
            Vector3 bottomRight = center + new Vector3(halfSize.x, -halfSize.y, 0);

            Debug.DrawLine(topLeft, topRight, color, duration);
            Debug.DrawLine(topRight, bottomRight, color, duration);
            Debug.DrawLine(bottomRight, bottomLeft, color, duration);
            Debug.DrawLine(bottomLeft, topLeft, color, duration);
        }

        /// <summary>
        /// 带颜色输出日志
        /// </summary>
        public static void Log(string message, Color color = default)
        {
            if (color == default)
            {
                Debug.Log(message);
            }
            else
            {
                Debug.Log($"<color={ColorToHex(color)}>{message}</color>");
            }
        }

        /// <summary>
        /// 输出错误
        /// </summary>
        public static void LogError(string message)
        {
            Debug.LogError(message);
        }

        /// <summary>
        /// 输出警告
        /// </summary>
        public static void LogWarning(string message)
        {
            Debug.LogWarning(message);
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        public static void MeasurePerformance(string operationName, System.Action operation)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            operation();
            stopwatch.Stop();

            Debug.Log($"Performance[{operationName}]: {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// 将颜色转换为16进制字符串
        /// </summary>
        private static string ColorToHex(Color color)
        {
            return ColorUtility.ToHtmlStringRGB(color);
        }
    }

    /// <summary>
    /// 层级工具
    /// </summary>
    public static class LayerUtils
    {
        /// <summary>
        /// 获取层级名称
        /// </summary>
        public static string GetLayerName(int layerIndex)
        {
            if (layerIndex >= 0 && layerIndex < 32)
            {
                return LayerMask.LayerToName(layerIndex);
            }
            return string.Empty;
        }

        /// <summary>
        /// 获取层级索引
        /// </summary>
        public static int GetLayerIndex(string layerName)
        {
            return LayerMask.NameToLayer(layerName);
        }

        /// <summary>
        /// 检查游戏对象是否在指定层级
        /// </summary>
        public static bool IsInLayer(GameObject obj, string layerName)
        {
            int layerIndex = GetLayerIndex(layerName);
            return obj.layer == layerIndex;
        }

        /// <summary>
        /// 设置游戏对象层级
        /// </summary>
        public static void SetLayer(GameObject obj, string layerName)
        {
            int layerIndex = GetLayerIndex(layerName);
            if (layerIndex != -1)
            {
                obj.layer = layerIndex;
            }
            else
            {
                Debug.LogWarning($"Layer '{layerName}' does not exist!");
            }
        }

        /// <summary>
        /// 设置游戏对象及其子对象层级
        /// </summary>
        public static void SetLayerRecursively(GameObject obj, string layerName)
        {
            SetLayer(obj, layerName);

            foreach (Transform child in obj.transform)
            {
                SetLayerRecursively(child.gameObject, layerName);
            }
        }
    }
}
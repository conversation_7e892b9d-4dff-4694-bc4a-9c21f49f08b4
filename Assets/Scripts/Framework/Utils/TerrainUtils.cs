using Unity.Entities;
using Unity.Mathematics;
using MyGame.Core; // For RequestTerrainPhysics

public static class TerrainUtils
{
    /// <summary>
    /// 在指定位置对地形施加一个爆炸效果。
    /// </summary>
    /// <param name="ecb">实体命令缓冲区，用于执行结构性更改。</param>
    /// <param name="mapEntity">地图实体的引用。</param>
    /// <param name="tileBuffer">地图瓦片数据的动态缓冲区。</param>
    /// <param name="explosionPos">爆炸中心的世界坐标。</param>
    /// <param name="radius">爆炸半径。</param>
    /// <param name="power">爆炸威力（地形高度变化量）。</param>
    public static void ApplyExplosion(
        EntityCommandBuffer ecb,
        DynamicBuffer<TileData> tileBuffer,
        int2 mapSize,
        float2 explosionPos,
        float radius,
        float power)
    {
        int2 min = (int2)math.max(0, explosionPos - radius);
        int2 max = (int2)math.min(mapSize - 1, explosionPos + radius);

        for (int y = min.y; y <= max.y; y++)
        {
            for (int x = min.x; x <= max.x; x++)
            {
                var pos = new int2(x, y);
                float dist = math.distance(pos, explosionPos);

                if (dist <= radius)
                {
                    float effect = (1 - (dist / radius)) * power; // Center has max effect
                    int index = y * mapSize.x + x;
                    
                    var tile = tileBuffer[index];
                    // Convert to float, apply the change, then convert back to uint.
                    float4 currentHeights = math.asfloat(tile.VertexHeights);
                    currentHeights -= effect; // Lower the terrain
                    tile.VertexHeights = math.asuint(currentHeights);
                    tileBuffer[index] = tile;
                }
            }
        }

        // After applying damage, trigger the physics simulation
        var entity = ecb.CreateEntity();
        ecb.AddComponent(entity, new RequestTerrainPhysics());
    }
}
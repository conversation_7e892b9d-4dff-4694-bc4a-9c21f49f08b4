using MyGame.Core.Systems.Jobs;
using Unity.Entities;
using Unity.Burst;
using UnityEngine;

/// <summary>
/// 地图生成监控系统 - 替代传统MonoBehaviour的协程监控
/// 负责监控地图生成状态、处理超时、触发事件
/// </summary>
[UpdateInGroup(typeof(LateSimulationSystemGroup))]
[BurstCompile]
public partial struct MapGeneratorMonitorSystem : ISystem
{
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<MapGenerationCompleted>();
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        // Since this system is event-driven, it will only run when the event exists.
        // The event is destroyed by FinalizeMapGenerationSystem, so this effectively runs once per generation.
        OnGenerationCompleted(ref state);
        
        // Disable the system until the next generation request, which is implicitly handled
        // by the event-driven nature of RequireForUpdate.
    }

    /// <summary>
    /// 处理生成完成事件
    /// </summary>
    private void OnGenerationCompleted(ref SystemState state)
    {
        // Debug.Log("✅ Map generation completed! Monitor system detected.");

        // 查找地图实体并显示统计信息
        // MapAspect is IAspect, not IComponentData, so we need to query for entities instead
        // TODO: Implement proper map aspect querying for statistics
        // For now, log that map generation completed without detailed statistics
        // Debug.Log("✅ Map generation completed - detailed statistics not yet implemented");
        
        // （可选）在这里触发其他系统，如AI生成、地形优化等
    }

    /// <summary>
    /// 显示生成统计信息 (当前未实现)
    /// </summary>
    private void DisplayGenerationStatistics()
    {
        // Debug.Log("📊 Map generation statistics display - implementation pending");
        // TODO: Implement proper statistics querying
    }

    [BurstCompile]
    public void OnDestroy(ref SystemState state) { }
}
<![CDATA[
#pragma kernel CSMain

struct Tile
{
    float4 VertexHeights;
    int Value; 
    // 其他数据，如果需要的话
};

RWStructuredBuffer<Tile> TileBuffer;
StructuredBuffer<int2> BrushIndices;
StructuredBuffer<float> BrushWeights;

int MapWidth;
int MapHeight;
int NumIterations;
int MaxLifetime;
float Inertia;
float SedimentCapacityFactor;
float MinSedimentCapacity;
float DepositSpeed;
float ErodeSpeed;
float EvaporateSpeed;
float Gravity;
int Radius;
uint Seed;

// 伪随机数生成器
float rand(float2 co)
{
    return frac(sin(dot(co.xy, float2(12.9898, 78.233))) * 43758.5453);
}

[numthreads(64,1,1)]
void CSMain (uint3 id : SV_DispatchThreadID)
{
    // 每个线程模拟一个水滴
    uint index = id.x;
    if (index >= NumIterations)
    {
        return;
    }

    // 初始化随机状态
    float2 randomSeed = float2(index, index * 0.5);
    
    // --- 以下逻辑从 C# HydraulicErosionJob 翻译而来 ---
    // 1. 初始化水滴属性 (位置, 速度, etc.)
    // 2. 开始水滴的生命周期循环 (for loop up to MaxLifetime)
    // 3. 在循环中:
    //    a. 计算当前位置的地形高度和梯度
    //    b. 更新水滴方向和速度
    //    c. 移动水滴
    //    d. 检查边界
    //    e. 计算泥沙承载力
    //    f. 侵蚀或沉积
    //    g. 将高度变化应用回 TileBuffer (注意：这里需要原子操作或接受竞态条件)
    //    h. 蒸发水分
}
]]>
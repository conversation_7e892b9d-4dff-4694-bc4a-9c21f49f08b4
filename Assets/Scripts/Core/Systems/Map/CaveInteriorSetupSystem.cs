using Unity.Burst;
using Unity.Entities;

/// <summary>
/// <PERSON><PERSON> requests to generate cave interiors by setting up the necessary components.
/// Its sole responsibility is structural changes.
/// </summary>
[BurstCompile]
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial struct CaveInteriorSetupSystem : ISystem
{
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<GenerateCaveInteriorRequest>();
        state.RequireForUpdate<BeginSimulationEntityCommandBufferSystem.Singleton>();
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var ecbSingleton = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>();
        var ecb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

        // Process requests: add a buffer and a tag to start the generation process.
        foreach (var (request, requestEntity) in SystemAPI.Query<RefRO<GenerateCaveInteriorRequest>>().WithEntityAccess())
        {
            // Check if the target entity is valid and has the required component.
            if (SystemAPI.HasComponent<CaveComponent>(request.ValueRO.CaveEntity))
            {
                // Defer adding the buffer and tag until the command buffer is played back.
                ecb.AddBuffer<CaveTileData>(request.ValueRO.CaveEntity);
                ecb.AddComponent<CaveIsGeneratingTag>(request.ValueRO.CaveEntity);
            }
            // The request has been handled, so destroy it.
            ecb.DestroyEntity(requestEntity);
        }
    }
}
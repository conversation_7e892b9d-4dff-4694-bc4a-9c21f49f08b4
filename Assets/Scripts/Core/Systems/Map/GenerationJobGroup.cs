using Unity.Entities;

namespace MyGame.Core.Systems.Map
{
    /// <summary>
    /// This group contains systems related to the asynchronous job-based map generation process.
    /// It will house the JobCompletionSystem to monitor and finalize generation jobs.
    /// </summary>
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    public partial class GenerationJobGroup : ComponentSystemGroup
    {
    }
}
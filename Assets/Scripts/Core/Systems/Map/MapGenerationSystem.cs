using Unity.Burst;
using Unity.Entities;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using Unity.Entities.LowLevel.Unsafe;
using Unity.Burst.Intrinsics;
using MyGame.Core.Systems.Jobs;
using MyGame.Core;
using MyGame.Core.Systems.Diagnostics;
using System.Runtime.InteropServices;

namespace MyGame.Core.Systems
{
    /// <summary>
    /// 烘焙到实体上的地形层数据，用于在 Burst Job 中访问。
    /// NOTE: Temporarily moved here to resolve a compiler issue.
    /// </summary>
    public struct TerrainLayerElement : IBufferElementData
    {
        public int TileType;
        public float HeightThreshold;
    }
    
    public struct HydraulicErosionConfig
    {
        public int NumIterations;
        public int Radius;
        public float Inertia;
        public float SedimentCapacityFactor;
        public float MinSedimentCapacity;
        public float ErodeSpeed;
        public float DepositSpeed;
        public float EvaporateSpeed;
        public float Gravity;
        public int MaxDropletLifetime;
        public float InitialWaterVolume;
        public float InitialSpeed;

        public HydraulicErosionConfig(bool defaults = true)
        {
            NumIterations = 50000;
            Radius = 3;
            Inertia = 0.05f;
            SedimentCapacityFactor = 4;
            MinSedimentCapacity = 0.01f;
            ErodeSpeed = 0.3f;
            DepositSpeed = 0.3f;
            EvaporateSpeed = 0.01f;
            Gravity = 4;
            MaxDropletLifetime = 30;
            InitialWaterVolume = 1;
            InitialSpeed = 1;
        }
    }
    
    /// <summary>
    /// 地图生成统计数据组件
    /// </summary>
    [BurstCompile]
    public struct MapGenerationStats : IComponentData
    {
        public int Width;
        public int Height;
        public int TotalTiles;
        public int FloorTiles;
        public int WaterTiles;
        public int ForestTiles;
        public int MountainTiles;
        public bool GenerationComplete;
        public bool ValidationCompleted;
    }

    public struct NativePriorityQueue<T> : System.IDisposable where T : struct
    {
        private NativeArray<T> nodes;
        private NativeArray<float> priorities;
        private int count;

        public NativePriorityQueue(int capacity, Allocator allocator)
        {
            nodes = new NativeArray<T>(capacity, allocator);
            priorities = new NativeArray<float>(capacity, allocator);
            count = 0;
        }

        public int Count => count;

        public void Enqueue(T node, float priority)
        {
            nodes[count] = node;
            priorities[count] = priority;
            count++;
            HeapifyUp(count - 1);
        }

        public T Dequeue()
        {
            T node = nodes[0];
            count--;
            nodes[0] = nodes[count];
            priorities[0] = priorities[count];
            HeapifyDown(0);
            return node;
        }

        private void HeapifyUp(int index)
        {
            int parent = (index - 1) / 2;
            while (index > 0 && priorities[index] < priorities[parent])
            {
                Swap(index, parent);
                index = parent;
                parent = (index - 1) / 2;
            }
        }

        private void HeapifyDown(int index)
        {
            int left = 2 * index + 1;
            int right = 2 * index + 2;
            int smallest = index;

            if (left < count && priorities[left] < priorities[smallest])
            {
                smallest = left;
            }
            if (right < count && priorities[right] < priorities[smallest])
            {
                smallest = right;
            }

            if (smallest != index)
            {
                Swap(index, smallest);
                HeapifyDown(smallest);
            }
        }

        private void Swap(int i, int j)
        {
            T tempNode = nodes[i];
            float tempPriority = priorities[i];
            nodes[i] = nodes[j];
            priorities[i] = priorities[j];
            nodes[j] = tempNode;
            priorities[j] = tempPriority;
        }

        public void Dispose()
        {
            nodes.Dispose();
            priorities.Dispose();
        }
    }

    /// <summary>
    /// 地图生成系统 - 纯数据驱动
    /// 输出纯数据结构，为游戏生成世界地图
    /// 包含地形、河流、森林和洞穴生成
    /// </summary>
    [UpdateInGroup(typeof(InitializationSystemGroup))]
    public partial class MapGenerationSystem : SystemBase
    {
        private ComputeShader erosionShader;
        private int kernelHandle;
        private NativeList<int2> brushIndices;
        private NativeList<float> brushWeights;
        
        // 噪声参数 - 保持原有的视觉效果
        private static readonly float2 NoiseScale1 = new float2(0.05f, 0.05f);
        private static readonly float2 NoiseScale2 = new float2(0.1f, 0.1f);
        private const float NoiseWeight1 = 0.7f;
        private const float NoiseWeight2 = 0.3f;

    protected override void OnCreate()
    {
        erosionShader = Resources.Load<ComputeShader>("Compute/HydraulicErosion");
        if (erosionShader != null)
        {
            kernelHandle = erosionShader.FindKernel("CSMain");
        }
        else
        {
            Debug.LogError("Compute shader 'HydraulicErosion' not found in Resources/Compute folder.");
        }
        brushIndices = new NativeList<int2>(Allocator.Persistent);
        brushWeights = new NativeList<float>(Allocator.Persistent);

        RequireForUpdate<RequestTerrainPhysics>();
        RequireForUpdate<TerrainLayerElement>();
    }

    protected override void OnDestroy()
    {
        if (brushIndices.IsCreated) brushIndices.Dispose();
        if (brushWeights.IsCreated) brushWeights.Dispose();
    }
    
    protected override void OnUpdate()
    {
        if (!SystemAPI.TryGetSingletonEntity<RequestTerrainPhysics>(out var requestEntity))
        {
            return;
        }

        if (!SystemAPI.TryGetSingleton<WorldMapSingleton>(out var mapSingleton))
        {
            return;
        }
        var mapEntity = mapSingleton.MapEntity;

        // --- Structural Changes ---
        // 1. Consume the request.
        EntityManager.DestroyEntity(requestEntity);
        
        // 2. Create the JobTracker entity.
        var jobTrackerEntity = EntityManager.CreateEntity();
        EntityManager.AddComponentData(jobTrackerEntity, new JobTrackerComponent
        {
            JobId = 1, // Using a hardcoded ID for map generation for now
            Status = JobStatus.Running,
            StartTime = SystemAPI.Time.ElapsedTime,
            TimeoutSeconds = 60.0f // 60-second timeout for map generation
        });

        // --- Data Retrieval & Job Scheduling ---
        var logBuffer = SystemAPI.GetSingletonBuffer<LogMessage>();
        var ecb = new EntityCommandBuffer(Allocator.Persistent);
        var ecbParallel = ecb.AsParallelWriter();

        var mapComponent = SystemAPI.GetComponent<MapComponent>(mapEntity);
        logBuffer.Add(new LogMessage { Value = $"[MapGenerationSystem] Starting generation with MapSize=({mapComponent.Width},{mapComponent.Height}) ChunkSize=({mapComponent.ChunkSize.x},{mapComponent.ChunkSize.y})." });
        
        var config = new MapConfigData
        {
            Width = mapComponent.Width,
            Height = mapComponent.Height,
            Seed = mapComponent.Seed,
            ChunkSize = mapComponent.ChunkSize,
            ForestDensity = mapComponent.ForestDensity,
            IsValid = true,
            MapEntity = mapEntity
        };

        var tileBuffer = SystemAPI.GetBuffer<TileData>(config.MapEntity);
        var treeBuffer = SystemAPI.GetBuffer<TreePosition>(config.MapEntity);
        
        // --- JOB CHAIN START ---
        
        // 1. Calculate Erosion Brush (Synchronously on the main thread)
        // This must be done before any jobs that might depend on it,
        // and crucially, before the GPU dispatch which requires these buffers.
        brushIndices.Clear();
        brushWeights.Clear();
        CalculateErosionBrush();

        // 2. Generate Base Terrain
        var dependency = this.Dependency; // Chain starts here now
        // 获取对地形配置缓冲区的只读访问权
        var terrainLayers = SystemAPI.GetSingletonBuffer<TerrainLayerElement>(true);

        var terrainJob = new TerrainGenerationJob
        {
            TileBuffer = tileBuffer,
            Size = new int2(config.Width, config.Height),
            Seed = config.Seed,
            TerrainLayers = terrainLayers // 将配置传递给 Job
        };
        dependency = terrainJob.Schedule(dependency);

        // --- SYNC POINT & GPU EROSION ---
        // We must complete the terrain generation to safely access the tile buffer on the CPU.
        dependency.Complete();

        // Run GPU-based hydraulic erosion synchronously.
        RunGpuHydraulicErosion(tileBuffer, config);
        
        // --- RESUME JOB CHAIN ---
        // Start a new dependency chain after the sync point, based on the handle we just completed.
        var combinedHandle = dependency;

        var finalizeTilesJob = new FinalizeTileTypesJob
        {
            TileBuffer = tileBuffer,
            WaterLevel = 5.0f
        };
        combinedHandle = finalizeTilesJob.Schedule(combinedHandle);

        var tempTreeQueue = new NativeQueue<TreePosition>(Allocator.TempJob);
        var forestJob = new IntegratedForestGenerationJob
        {
            TileBuffer = tileBuffer,
            TreeBuffer = tempTreeQueue.AsParallelWriter(),
            MapSize = new int2(config.Width, config.Height),
            ChunkSize = config.ChunkSize,
            Seed = (uint)(config.Seed + 300),
            ForestDensity = config.ForestDensity
        };
        int chunksX = (config.Width + config.ChunkSize.x - 1) / config.ChunkSize.x;
        int chunksY = (config.Height + config.ChunkSize.y - 1) / config.ChunkSize.y;
        combinedHandle = forestJob.Schedule(chunksX * chunksY, 1, combinedHandle);

        var copyTreesJob = new CopyTreesFromQueueJob
        {
            TreeQueue = tempTreeQueue,
            TreeBuffer = treeBuffer
        };
        combinedHandle = copyTreesJob.Schedule(combinedHandle);
        combinedHandle = tempTreeQueue.Dispose(combinedHandle);
        
        var caveJob = new IntegratedCaveGenerationJob
        {
            TileBuffer = tileBuffer,
            MapSize = new int2(config.Width, config.Height),
            Seed = (uint)(config.Seed + 100),
            EntityCommandBuffer = ecbParallel
        };
        combinedHandle = caveJob.Schedule(combinedHandle);

        var chunkJob = new ChunkGenerationJob
        {
            TileGrid = tileBuffer,
            MapSize = new int2(config.Width, config.Height),
            ChunkSize = new int2(config.ChunkSize.x, config.ChunkSize.y),
            Seed = config.Seed,
            EntityCommandBuffer = ecbParallel
        };
        chunksX = (config.Width + config.ChunkSize.x - 1) / config.ChunkSize.x;
        chunksY = (config.Height + config.ChunkSize.y - 1) / config.ChunkSize.y;
        combinedHandle = chunkJob.Schedule(chunksX * chunksY, 1, combinedHandle);

        var statsJob = new GenerationStatisticsJob
        {
            MapEntity = config.MapEntity,
            TileBuffer = tileBuffer,
            MapSize = new int2(config.Width, config.Height),
            EntityCommandBuffer = ecbParallel,
            LogBuffer = logBuffer
        };
        combinedHandle = statsJob.Schedule(combinedHandle);

        // --- JOB CHAIN END ---

        // Register the final handle with the JobManager
        var jobManager = World.GetExistingSystemManaged<JobManagerSystem>().JobManager;
        jobManager.RegisterJob(jobTrackerEntity, combinedHandle, ecb);

        // Pass the final dependency back to the system
        this.Dependency = combinedHandle;
    }

    private void CalculateErosionBrush()
    {
        // Must call the constructor with the parameter to ensure default values are set for the struct.
        int radius = new HydraulicErosionConfig(true).Radius;
        float weightSum = 0;
        for (int y = -radius; y <= radius; y++)
        {
            for (int x = -radius; x <= radius; x++)
            {
                float sqrDst = x * x + y * y;
                if (sqrDst < radius * radius)
                {
                    brushIndices.Add(new int2(x, y));
                    float weight = 1 - math.sqrt(sqrDst) / radius;
                    weightSum += weight;
                    brushWeights.Add(weight);
                }
            }
        }

        if (weightSum > 0)
        {
            for (int i = 0; i < brushWeights.Length; i++)
            {
                brushWeights[i] /= weightSum;
            }
        }
    }

    private void RunGpuHydraulicErosion(DynamicBuffer<TileData> tileBuffer, MapConfigData mapConfig)
    {
        if (erosionShader == null)
        {
            Debug.LogError("Cannot run GPU erosion: Compute Shader is not loaded.");
            return;
        }

        // Must call the constructor with the parameter to ensure default values are set for the struct.
        var erosionConfig = new HydraulicErosionConfig(true);
        
        // 1. Create Compute Buffers
        var tileComputeBuffer = new ComputeBuffer(tileBuffer.Length, Marshal.SizeOf<TileData>());
        var brushIndicesComputeBuffer = new ComputeBuffer(brushIndices.Length, Marshal.SizeOf<int2>());
        var brushWeightsComputeBuffer = new ComputeBuffer(brushWeights.Length, Marshal.SizeOf<float>());
        
        // 2. Upload Data to GPU
        tileComputeBuffer.SetData(tileBuffer.AsNativeArray());
        brushIndicesComputeBuffer.SetData(brushIndices.AsArray());
        brushWeightsComputeBuffer.SetData(brushWeights.AsArray());
        
        // 3. Set Shader Parameters
        erosionShader.SetBuffer(kernelHandle, "TileBuffer", tileComputeBuffer);
        erosionShader.SetBuffer(kernelHandle, "BrushIndices", brushIndicesComputeBuffer);
        erosionShader.SetBuffer(kernelHandle, "BrushWeights", brushWeightsComputeBuffer);
        erosionShader.SetInts("mapSize", mapConfig.Width, mapConfig.Height);
        erosionShader.SetInt("brushSize", brushIndices.Length);
        erosionShader.SetInt("numIterations", erosionConfig.NumIterations);
        erosionShader.SetFloat("inertia", erosionConfig.Inertia);
        erosionShader.SetFloat("sedimentCapacityFactor", erosionConfig.SedimentCapacityFactor);
        erosionShader.SetFloat("minSedimentCapacity", erosionConfig.MinSedimentCapacity);
        erosionShader.SetFloat("erodeSpeed", erosionConfig.ErodeSpeed);
        erosionShader.SetFloat("depositSpeed", erosionConfig.DepositSpeed);
        erosionShader.SetFloat("evaporateSpeed", erosionConfig.EvaporateSpeed);
        erosionShader.SetFloat("gravity", erosionConfig.Gravity);
        erosionShader.SetInt("maxDropletLifetime", erosionConfig.MaxDropletLifetime);
        erosionShader.SetFloat("initialWaterVolume", erosionConfig.InitialWaterVolume);
        erosionShader.SetFloat("initialSpeed", erosionConfig.InitialSpeed);
        erosionShader.SetInt("seed", mapConfig.Seed);

        // 4. Dispatch
        int threadGroups = (erosionConfig.NumIterations + 63) / 64;
        erosionShader.Dispatch(kernelHandle, threadGroups, 1, 1);

        // 5. Get Data Back (Blocking)
        // We need an intermediate managed array to get data back from the GPU.
        var tilesFromGpu = new TileData[tileBuffer.Length];
        tileComputeBuffer.GetData(tilesFromGpu);
        tileBuffer.CopyFrom(new NativeArray<TileData>(tilesFromGpu, Allocator.Temp));


        // 6. Release resources
        tileComputeBuffer.Release();
        brushIndicesComputeBuffer.Release();
        brushWeightsComputeBuffer.Release();
    }

    // Aspect is gone, structs are now private nested in the system struct
    
// =======================================================================
// 数据结构与准备作业
// =======================================================================
[BurstCompile]
private struct MapConfigData
{
    public int Width;
    public int Height;
    public int Seed;
    public int2 ChunkSize;
    public float ForestDensity;
    public bool IsValid;
    public Entity MapEntity;
}

// ActivateAndCopyDataJob is no longer needed with the new request-driven approach.
       
      
// =======================================================================
// 地图生成Job实现
// =======================================================================


    [BurstCompile]
    private partial struct IntegratedCaveGenerationJob : IJob
    {
        public DynamicBuffer<TileData> TileBuffer; // Writes to buffer
        public int2 MapSize;
        public uint Seed;
        public EntityCommandBuffer.ParallelWriter EntityCommandBuffer;

        public void Execute()
        {
            var tileArray = TileBuffer.AsNativeArray();
            var random = new Unity.Mathematics.Random(Seed);
            const int numCavesToPlace = 15;
            const int MaxAttemptsPerCave = 10;
            
            // IJob 使用 ParallelWriter 时需要一个固定的 sortKey
            int sortKey = 0;

            for(int i = 0; i < numCavesToPlace; i++)
            {
                for (int attempt = 0; attempt < MaxAttemptsPerCave; attempt++)
                {
                    int2 potentialPos = random.NextInt2(new int2(1, 1), MapSize - 1);
                    int tileIndex = potentialPos.y * MapSize.x + potentialPos.x;
 
                    if (tileArray[tileIndex].Value == (int)TileType.Mountain)
                    {
                        tileArray[tileIndex] = new TileData { Value = (int)TileType.CaveEntrance };
                        
                        var caveEntity = EntityCommandBuffer.CreateEntity(sortKey);
                        EntityCommandBuffer.AddComponent(sortKey, caveEntity, new CaveComponent
                        {
                            CaveID = random.NextUInt(),
                            EntrancePosition = potentialPos,
                            IsGenerated = 0,
                            InteriorSize = new int2(64, 64)
                        });

                        break;
                    }
                }
            }
        }
    }

    [BurstCompile]
    private partial struct IntegratedForestGenerationJob : IJobParallelFor
    {
        [ReadOnly] public DynamicBuffer<TileData> TileBuffer;
        public NativeQueue<TreePosition>.ParallelWriter TreeBuffer;
        public int2 MapSize;
        public int2 ChunkSize;
        public uint Seed;
        public float ForestDensity;

        public void Execute(int index)
        {
            var tileArray = TileBuffer.AsNativeArray();
            var random = new Unity.Mathematics.Random(Seed + (uint)index);

            int chunksX = (MapSize.x + ChunkSize.x - 1) / ChunkSize.x;
            int chunkX = index % chunksX;
            int chunkY = index / chunksX;
            int2 regionStart = new int2(chunkX * ChunkSize.x, chunkY * ChunkSize.y);
            int2 regionEnd = math.min(regionStart + ChunkSize, MapSize);

            for (int y = regionStart.y; y < regionEnd.y; y++)
            {
                for (int x = regionStart.x; x < regionEnd.x; x++)
                {
                    int tileIndex = y * MapSize.x + x;
                    if (tileArray[tileIndex].Value == (int)TileType.Floor)
                    {
                        // Use noise to determine forest areas
                        float noise = Unity.Mathematics.noise.cnoise(new float2(x, y) * 0.1f);
                        if (noise > 0.6f)
                        {
                            // If it's a forest area, place a tree based on density probability
                            if (random.NextFloat() < ForestDensity)
                            {
                                // Add a small random offset to the tree position for a more natural look
                                float2 treePos = new float2(x, y) + random.NextFloat2(-0.4f, 0.4f);
                                TreeBuffer.Enqueue(new TreePosition { Value = treePos });
                            }
                        }
                    }
                }
            }
        }
    }

    [BurstCompile]
    private partial struct CopyTreesFromQueueJob : IJob
    {
        public NativeQueue<TreePosition> TreeQueue;
        public DynamicBuffer<TreePosition> TreeBuffer;

        public void Execute()
        {
            TreeBuffer.Clear();
            while(TreeQueue.TryDequeue(out var tree))
            {
                TreeBuffer.Add(tree);
            }
        }
    }

    [BurstCompile]
    private partial struct GenerationStatisticsJob : IJob
    {
        public Entity MapEntity;
        [ReadOnly] public DynamicBuffer<TileData> TileBuffer;
        public int2 MapSize;
        public EntityCommandBuffer.ParallelWriter EntityCommandBuffer;
        public DynamicBuffer<LogMessage> LogBuffer;

        public void Execute()
        {
            int4 tileCounts = new int4(0, 0, 0, 0);
            int length = TileBuffer.Length;

            for (int i = 0; i < length - 3; i += 4)
            {
                int4 tileValues = new int4(
                    TileBuffer[i].Value,
                    TileBuffer[i + 1].Value,
                    TileBuffer[i + 2].Value,
                    TileBuffer[i + 3].Value
                );
                
                tileCounts += math.select(int4.zero, new int4(1, 0, 0, 0), tileValues == 2); // Floor
                tileCounts += math.select(int4.zero, new int4(0, 1, 0, 0), tileValues == 3); // Water
                tileCounts += math.select(int4.zero, new int4(0, 0, 0, 1), tileValues == 5); // Mountain
            }
            
            for (int i = (length / 4) * 4; i < length; i++)
            {
                switch (TileBuffer[i].Value)
                {
                    case 2: tileCounts.x++; break;
                    case 3: tileCounts.y++; break;
                    case 5: tileCounts.w++; break;
                }
            }

            var stats = new MapGenerationStats
            {
                Width = MapSize.x,
                Height = MapSize.y,
                TotalTiles = MapSize.x * MapSize.y,
                FloorTiles = tileCounts.x,
                WaterTiles = tileCounts.y,
                ForestTiles = 0,
                MountainTiles = tileCounts.w,
                GenerationComplete = true,
                ValidationCompleted = true
            };
            
            // IJob 使用 ParallelWriter 时需要一个固定的 sortKey
            EntityCommandBuffer.AddComponent(0, MapEntity, stats);
            LogBuffer.Add(new LogMessage { Value = $"[MapGenerationSystem] Generation jobs complete. {stats.TotalTiles} tiles created." });
        }
    }

    // 标记组件, now moved to Core/Components/Map/MapGenerationTags.cs
    
    [BurstCompile]
    private partial struct TerrainGenerationJob : IJob
    {
        public DynamicBuffer<TileData> TileBuffer;
        [ReadOnly] public int2 Size;
        [ReadOnly] public int Seed;
        [ReadOnly] public DynamicBuffer<TerrainLayerElement> TerrainLayers;

        public void Execute()
        {
            TileBuffer.ResizeUninitialized(Size.x * Size.y);
            var tileArray = TileBuffer.AsNativeArray();

            for (int index = 0; index < tileArray.Length; index++)
            {
                int2 gridPos = new int2(index % Size.x, index / Size.x);

                float2 noiseScale1 = new float2(0.05f, 0.05f);
                float2 noiseScale2 = new float2(0.1f, 0.1f);
                const float NoiseWeight1 = 0.7f;
                const float NoiseWeight2 = 0.3f;

                float2 noisePos1 = new float2(gridPos.x, gridPos.y) * noiseScale1;
                float2 noisePos2 = new float2(gridPos.x, gridPos.y) * noiseScale2;

                float noise1 = Unity.Mathematics.noise.cnoise(noisePos1) * 0.5f + 0.5f;
                float noise2 = Unity.Mathematics.noise.cnoise(noisePos2) * 0.5f + 0.5f;

                float finalNoise = noise1 * NoiseWeight1 + noise2 * NoiseWeight2;
                
                float height = finalNoise * 20;

                int tileType = GetTerrainTypeFromHeight(height);
                
                uint heightAsUint = math.asuint(height);
                tileArray[index] = new TileData
                {
                    Value = tileType,
                    VertexHeights = new uint4(heightAsUint, heightAsUint, heightAsUint, heightAsUint)
                };
            }
        }

        private int GetTerrainTypeFromHeight(float height)
        {
            // 默认地形为最高级别的地形
            int tileType = TerrainLayers.Length > 0 ? TerrainLayers[TerrainLayers.Length - 1].TileType : (int)TileType.Empty;

            // 由于配置是按高度升序排列的，我们找到第一个低于或等于当前高度的阈值，
            // 那么当前高度就属于这个阈值对应的地形类型。
            foreach (var layer in TerrainLayers)
            {
                if (height <= layer.HeightThreshold)
                {
                    return layer.TileType;
                }
            }
            return tileType;
        }
    }

    [BurstCompile]
    private partial struct ChunkGenerationJob : IJobParallelFor
    {
        [ReadOnly] public DynamicBuffer<TileData> TileGrid; // This is now safe to read from.
        [ReadOnly] public int2 MapSize;
        [ReadOnly] public int2 ChunkSize;
        [ReadOnly] public int Seed;

        public EntityCommandBuffer.ParallelWriter EntityCommandBuffer;

        public void Execute(int index)
        {
            var random = new Unity.Mathematics.Random((uint)(Seed + index));

            int chunksX = (MapSize.x + ChunkSize.x - 1) / ChunkSize.x;
            int x = index % chunksX;
            int y = index / chunksX;

            var chunkEntity = EntityCommandBuffer.CreateEntity(index);
            var chunkPos = new int2(x, y);

            var chunkComponent = new ChunkComponent
            {
                ChunkPosition = chunkPos,
                ChunkSize = new int2(ChunkSize.x, ChunkSize.y),
                WorldPosition = new int2(chunkPos.x * ChunkSize.x, chunkPos.y * ChunkSize.y),
                DetailLevel = 1,
                IsGenerated = true,
                NoiseSeed = (int)(Seed + (uint)(x * chunksX + y))
            };

            DetermineChunkType(ref chunkComponent, ref random);

            EntityCommandBuffer.AddComponent(index, chunkEntity, chunkComponent);
        }

        private void DetermineChunkType(ref ChunkComponent chunk, ref Unity.Mathematics.Random random)
        {
            int2 center = chunk.WorldPosition + chunk.ChunkSize / 2;
            if (center.x >= 0 && center.x < MapSize.x &&
                center.y >= 0 && center.y < MapSize.y)
            {
                int index = center.y * MapSize.x + center.x;
                int terrainSample = TileGrid[index].Value;

                chunk.ChunkType = GetChunkTypeFromTerrain(terrainSample);
                chunk.ThemeType = GetThemeTypeFromTerrain(terrainSample);
            }
            else
            {
                chunk.ChunkType = ChunkType.Plains;
                chunk.ThemeType = ThemeType.Plains;
            }

            if (random.NextFloat() < 0.2f)
            {
                chunk.ChunkType = ChunkType.Village;
                chunk.ThemeType = ThemeType.Plains;
            }
        }

        private static ChunkType GetChunkTypeFromTerrain(int terrainSample)
        {
            return (TileType)terrainSample switch
            {
                TileType.Water => ChunkType.Plains,
                TileType.Mountain => ChunkType.Cave,     // Chunks with mountains are considered "Cave" areas
                TileType.CaveEntrance => ChunkType.Cave, // Entrances are also in "Cave" chunks
                _ => ChunkType.Plains
            };
        }

        private static ThemeType GetThemeTypeFromTerrain(int terrainSample)
        {
            return (TileType)terrainSample switch
            {
                TileType.Mountain => ThemeType.Mountain,
                TileType.Water => ThemeType.Plains,
                TileType.CaveEntrance => ThemeType.Mountain, // Entrances use mountain theme
                _ => ThemeType.Plains
            };
        }
        
    }
    [BurstCompile]
    private partial struct FinalizeTileTypesJob : IJob
    {
        public DynamicBuffer<TileData> TileBuffer;
        public float WaterLevel;

        public void Execute()
        {
            for (int i = 0; i < TileBuffer.Length; i++)
            {
                var tile = TileBuffer[i];
                // Convert uint heights back to float for calculation.
                float4 heights = math.asfloat(tile.VertexHeights);
                // If a tile's average height is below water level, classify it as water.
                if (math.csum(heights) * 0.25f < WaterLevel)
                {
                    if (tile.Value != (int)TileType.Water)
                    {
                        tile.Value = (int)TileType.Water;
                        TileBuffer[i] = tile;
                    }
                }
            }
        }
    }
}
}
using Unity.Burst;
using Unity.Entities;
using MyGame.Core.Systems.Jobs;
using UnityEngine;
using MyGame.Core.Systems.Diagnostics;

namespace MyGame.Core.Systems.Map
{
    [UpdateInGroup(typeof(LateSimulationSystemGroup))]
    public partial struct FinalizeMapGenerationSystem : ISystem
    {
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<MapGenerationCompleted>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            var count = SystemAPI.QueryBuilder().WithAll<ChunkComponent>().Build().CalculateEntityCount();
            var logBuffer = SystemAPI.GetSingletonBuffer<LogMessage>();
            logBuffer.Add(new LogMessage { Value = $"[FinalizeMapGenerationSystem] Chunk entities count: {count}" });

            var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged);

            foreach (var (evt, entity) in SystemAPI.Query<RefRO<MapGenerationCompleted>>().WithEntityAccess())
            {
                // Destroy the JobTracker entity that has now completed.
                if(SystemAPI.Exists(evt.ValueRO.JobTrackerEntity))
                {
                    ecb.DestroyEntity(evt.ValueRO.JobTrackerEntity);
                }

                // Destroy the event entity itself to consume it.
                ecb.DestroyEntity(entity);
            }
        }
    }
}
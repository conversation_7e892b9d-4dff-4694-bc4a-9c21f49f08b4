using Unity.Burst;
using Unity.Entities;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using MyGame.Core;

[BurstCompile]
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(CaveInteriorSetupSystem))]
public partial struct CaveInteriorGenerationSystem : ISystem
{
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<CaveIsGeneratingTag>();
        state.RequireForUpdate<BeginSimulationEntityCommandBufferSystem.Singleton>();
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var ecbSingleton = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>();

        var job = new CaveGenerationJob
        {
            ECB = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged).AsParallelWriter(),
            ElapsedTime = SystemAPI.Time.ElapsedTime,
        };
        
        state.Dependency = job.ScheduleParallel(state.Dependency);
    }

    [BurstCompile]
    [WithAll(typeof(CaveIsGeneratingTag))]
    public partial struct CaveGenerationJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        public double ElapsedTime;

        public void Execute(Entity entity, [ChunkIndexInQuery] int chunkIndex, ref CaveComponent cave, DynamicBuffer<CaveTileData> caveTilesBuffer)
        {
            if (cave.IsGenerated == 1) return;

            caveTilesBuffer.ResizeUninitialized(cave.InteriorSize.x * cave.InteriorSize.y);

            var random = new Random((uint)entity.Index + (uint)ElapsedTime);
            uint seed = random.NextUInt();

            var handle = new JobHandle();

            var initJob = new InitJob
            {
                CaveTiles = caveTilesBuffer.AsNativeArray(),
            };
            handle = initJob.Schedule(caveTilesBuffer.Length, 64, handle);

            var walkerJob = new WalkerJob
            {
                CaveTiles = caveTilesBuffer.AsNativeArray(),
                Size = cave.InteriorSize,
                Seed = seed
            };
            handle = walkerJob.Schedule(10, 1, handle);

            var roomJob = new RoomDiggingJob
            {
                CaveTiles = caveTilesBuffer.AsNativeArray(),
                Size = cave.InteriorSize,
                Seed = seed + 1
            };
            handle = roomJob.Schedule(15, 1, handle);

            var smoothedTiles = new NativeArray<CaveTileData>(caveTilesBuffer.Length, Allocator.TempJob);
            var smoothJob = new SmoothJob
            {
                InputTiles = caveTilesBuffer.AsNativeArray(),
                OutputTiles = smoothedTiles,
                Size = cave.InteriorSize
            };
            handle = smoothJob.Schedule(caveTilesBuffer.Length, 64, handle);

            var copyJob = new CopyJob
            {
                Source = smoothedTiles,
                Destination = caveTilesBuffer.AsNativeArray()
            };
            handle = copyJob.Schedule(handle);

            smoothedTiles.Dispose(handle);
            
            handle.Complete();

            cave.IsGenerated = 1;
            ECB.RemoveComponent<CaveIsGeneratingTag>(chunkIndex, entity);
        }
    }

    [BurstCompile]
    private struct InitJob : IJobParallelFor
    {
        public NativeArray<CaveTileData> CaveTiles;
        public void Execute(int index)
        {
            CaveTiles[index] = new CaveTileData { Value = new TileData { Value = (int)TileType.Wall } };
        }
    }

    [BurstCompile]
    private struct WalkerJob : IJobParallelFor
    {
        public NativeArray<CaveTileData> CaveTiles;
        public int2 Size;
        public uint Seed;

        public void Execute(int index)
        {
            var random = new Random(Seed + (uint)index);
            int2 currentPos = Size / 2;
            int2 direction = random.NextInt2(-1, 2);
            if(math.all(direction == 0)) direction.x = 1;

            for (int step = 0; step < 200; step++)
            {
                if (currentPos.x < 1 || currentPos.x >= Size.x - 1 || currentPos.y < 1 || currentPos.y >= Size.y - 1)
                    break;

                int tileIndex = currentPos.y * Size.x + currentPos.x;
                CaveTiles[tileIndex] = new CaveTileData { Value = new TileData { Value = (int)TileType.Floor } };

                if (random.NextFloat() < 0.3f)
                {
                    int2 newDir = random.NextInt2(-1, 2);
                    if(math.all(newDir == 0)) newDir.x = -1;
                    direction = newDir;
                }
                currentPos += direction;
            }
        }
    }

    [BurstCompile]
    private struct RoomDiggingJob : IJobParallelFor
    {
        public NativeArray<CaveTileData> CaveTiles;
        public int2 Size;
        public uint Seed;

        public void Execute(int index)
        {
            var random = new Random(Seed + (uint)index);
            int maxRadius = 8;

            int radius = random.NextInt(3, maxRadius);
            int2 center = random.NextInt2(radius, Size - radius);

            // Only place a room if the center is already a floor (i.e., part of a tunnel)
            if (CaveTiles[center.y * Size.x + center.x].Value.Value != (int)TileType.Floor) return;

            for (int y = -radius; y <= radius; y++)
            {
                for (int x = -radius; x <= radius; x++)
                {
                    if (x * x + y * y <= radius * radius)
                    {
                        int2 pos = center + new int2(x, y);
                        if (pos.x >= 0 && pos.x < Size.x && pos.y >= 0 && pos.y < Size.y)
                        {
                            CaveTiles[pos.y * Size.x + pos.x] = new CaveTileData { Value = new TileData { Value = (int)TileType.Floor } };
                        }
                    }
                }
            }
        }
    }

    [BurstCompile]
    private struct SmoothJob : IJobParallelFor
    {
        [ReadOnly] public NativeArray<CaveTileData> InputTiles;
        public NativeArray<CaveTileData> OutputTiles;
        public int2 Size;

        public void Execute(int index)
        {
            int2 pos = new int2(index % Size.x, index / Size.x);
            if (pos.x == 0 || pos.x == Size.x - 1 || pos.y == 0 || pos.y == Size.y - 1)
            {
                OutputTiles[index] = new CaveTileData { Value = new TileData { Value = (int)TileType.Wall } };
                return;
            }

            int wallNeighbours = 0;
            for (int y = -1; y <= 1; y++)
            {
                for (int x = -1; x <= 1; x++)
                {
                    if (x == 0 && y == 0) continue;
                    int2 neighbourPos = pos + new int2(x, y);
                    if (InputTiles[neighbourPos.y * Size.x + neighbourPos.x].Value.Value == (int)TileType.Wall)
                    {
                        wallNeighbours++;
                    }
                }
            }

            if (InputTiles[index].Value.Value == (int)TileType.Wall && wallNeighbours < 4)
            {
                OutputTiles[index] = new CaveTileData { Value = new TileData { Value = (int)TileType.Floor } };
            }
            else if (InputTiles[index].Value.Value == (int)TileType.Floor && wallNeighbours > 4)
            {
                OutputTiles[index] = new CaveTileData { Value = new TileData { Value = (int)TileType.Wall } };
            }
            else
            {
                OutputTiles[index] = InputTiles[index];
            }
        }
    }

    [BurstCompile]
    private struct CopyJob : IJob
    {
        [ReadOnly] public NativeArray<CaveTileData> Source;
        public NativeArray<CaveTileData> Destination;

        public void Execute()
        {
            Source.CopyTo(Destination);
        }
    }
}
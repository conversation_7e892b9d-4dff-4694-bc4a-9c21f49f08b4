using Unity.Entities;
using Unity.Collections;
using System.Net.Http;
using System.Threading.Tasks;
using UnityEngine;

/// <summary>
/// 处理网络请求的DOTS系统。
/// 这个系统不能被Burst编译，因为它处理托管对象和异步操作。
/// </summary>
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial struct NetworkSystem : ISystem
{
    private static readonly HttpClient client = new HttpClient();

    public void OnCreate(ref SystemState state)
    {
        // 系统在没有网络请求时不需要更新。
        state.RequireForUpdate<NetworkRequest>();
    }

    public void OnUpdate(ref SystemState state)
    {
        // 因为HttpClient的异步方法不能在struct的成员函数中直接await，
        // 我们使用一个立即执行的静态异步方法来包装它。
        // EntityCommandBuffer用于在主线程上安全地修改实体。
        var ecb = new EntityCommandBuffer(Allocator.Temp);

        foreach (var (request, entity) in SystemAPI.Query<RefRO<NetworkRequest>>().WithEntityAccess())
        {
            // 对于每个请求，发起一个异步网络操作。
            ProcessRequestAsync(request.ValueRO, entity, ecb);
            
            // 处理完请求后，从实体上移除请求组件，以防止重复处理。
            ecb.RemoveComponent<NetworkRequest>(entity);
        }

        ecb.Playback(state.EntityManager);
        ecb.Dispose();
    }

    private static async void ProcessRequestAsync(NetworkRequest request, Entity entity, EntityCommandBuffer ecb)
    {
        var responseData = new NetworkResponse { IsSuccess = false };
        string url = "https://jsonplaceholder.typicode.com/posts"; // 使用一个假的API端点进行测试

        try
        {
            // 模拟发送数据 (在实际应用中，你会使用request.Data)
            HttpContent content = new StringContent("{\"title\":\"foo\",\"body\":\"bar\",\"userId\":1}", System.Text.Encoding.UTF8, "application/json");
            
            HttpResponseMessage response = await client.PostAsync(url, content);
            
            if (response.IsSuccessStatusCode)
            {
                string responseBody = await response.Content.ReadAsStringAsync();
                responseData.IsSuccess = true;
                responseData.ResponseData = new FixedString4096Bytes(responseBody);
                Debug.Log($"[NetworkSystem] Success: {responseBody}");
            }
            else
            {
                responseData.ResponseData = new FixedString4096Bytes($"Error: {response.StatusCode}");
                Debug.LogError($"[NetworkSystem] Error: {response.StatusCode}");
            }
        }
        catch (HttpRequestException e)
        {
            responseData.ResponseData = new FixedString4096Bytes($"Request Exception: {e.Message}");
            Debug.LogError($"[NetworkSystem] Request Exception: {e.Message}");
        }

        // 将响应组件添加回主线程上的实体。
        ecb.AddComponent(entity, responseData);
    }
}
using Unity.Burst;
using Unity.Entities;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;

/// <summary>
/// Unity 6 对象池系统 - 零垃圾收集的动态对象管理
/// 使用手动实现的Native集合对象池实现高效的实例回收复用
/// </summary>
[UpdateInGroup(typeof(SimulationSystemGroup))]
[BurstCompile]
public partial struct ObjectPoolSystem : ISystem
{
    /// <summary>
    /// 对象池统计信息 - 公共访问以支持监控
    /// </summary>
    public struct PoolStatistics
    {
        public int TotalObjectsCreated;
        public int ActiveObjectsCount;
        public int PooledObjectsCount;
        public int PeakUsageCount;
        public float AvgReuseRate;
    }

    private PoolStatistics globalStats;
    private EntityQuery objectUsageQuery;
    private bool hasPerformedWarmup;

    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        // 初始化统计信息
        globalStats = new PoolStatistics();

        // 查询所有使用对象池的对象实体
        objectUsageQuery = new EntityQueryBuilder(Allocator.Temp)
            .WithAll<PoolableObjectComponent>()
            .Build(ref state);
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        // 更新对象池统计信息
        UpdatePoolStatistics(ref state);

        // 自动清理过期对象
        CleanupExpiredPooledObjects(ref state);

        // 预热关键对象池（warmup）
        if (!hasPerformedWarmup)
        {
            PerformPoolWarmup(ref state);
            hasPerformedWarmup = true;
        }
    }

    [BurstCompile]
    public void OnDestroy(ref SystemState state)
    {
        // 释放所有对象池资源
        // EntityQueries created via GetEntityQuery or EntityQueryBuilder are owned by the system
        // and should not be disposed manually. They will be disposed when the system is destroyed.
    }

    /// <summary>
    /// 更新对象池统计信息
    /// </summary>
    private void UpdatePoolStatistics(ref SystemState state)
    {
        if (SystemAPI.Time.ElapsedTime % 1.0f < SystemAPI.Time.DeltaTime) // 每秒更新一次
        {
            var entities = objectUsageQuery.ToEntityArray(Allocator.Temp);
            globalStats.ActiveObjectsCount = entities.Length;

            // 计算重用率
            if (globalStats.TotalObjectsCreated > 0)
            {
                globalStats.AvgReuseRate = (float)globalStats.PooledObjectsCount /
                    (globalStats.TotalObjectsCreated + globalStats.PooledObjectsCount);
            }

            entities.Dispose();
        }
    }

    /// <summary>
    /// 清理过期对象
    /// </summary>
    private void CleanupExpiredPooledObjects(ref SystemState state)
    {
        var cleanupJob = new CleanupExpiredObjectsJob
        {
            CurrentTime = SystemAPI.Time.ElapsedTime,
            EntityCommandBuffer = SystemAPI.GetSingleton<BeginInitializationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged)
        };

        // 过滤需要清理的对象并更新系统依赖
        JobHandle cleanupJobHandle = cleanupJob.Schedule(objectUsageQuery, state.Dependency);
        state.Dependency = JobHandle.CombineDependencies(state.Dependency, cleanupJobHandle);
    }

    /// <summary>
    /// 预热对象池
    /// </summary>
    private void PerformPoolWarmup(ref SystemState state)
    {
        // 在第一次运行时预热关键对象池
        // 预热Map生成系统所需的缓冲区对象池，这里预留空实现
    }

    /// <summary>
    /// 获取对象池统计信息
    /// </summary>
    public PoolStatistics GetPoolStatistics()
    {
        return globalStats;
    }

    /// <summary>
    /// 重置对象池统计
    /// </summary>
    public void ResetPoolStatistics()
    {
        globalStats = new PoolStatistics();
    }

    /// <summary>
    /// 清理过期对象的Job
    /// </summary>
    [BurstCompile]
    private partial struct CleanupExpiredObjectsJob : IJobEntity
    {
        public double CurrentTime;
        public EntityCommandBuffer EntityCommandBuffer;

        public void Execute(ref PoolableObjectComponent poolable, Entity entity)
        {
            // 检查对象是否过期
            if (poolable.IsExpired && poolable.ExpiryTime <= CurrentTime)
            {
                // 将对象返回对象池或销毁
                EntityCommandBuffer.DestroyEntity(entity);

                // 更新统计信息
                // 可以在这里添加返回对象池的逻辑
            }
        }
    }
}

/// <summary>
/// 可对象池化的对象组件标记
/// </summary>
[BurstCompile]
public struct PoolableObjectComponent : IComponentData
{
    public bool IsExpired;
    public double ExpiryTime;
    public Entity OwnerEntity;          // 拥有者实体（用于权限检查）
    public int PoolId;                 // 对象池ID
    public FixedString32Bytes PoolName; // 对象池名称
}
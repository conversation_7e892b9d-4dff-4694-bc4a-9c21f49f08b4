using UnityEngine;
using UnityEngine.Pool; // 1. 引入官方API命名空间

/// <summary>
/// 玩家对象池系统 - 使用官方UnityEngine.Pool实现
/// </summary>
public class PlayerObjectPool : MonoBehaviour
{
    [Header("对象池配置")]
    [SerializeField] private GameObject playerPrefab;
    [SerializeField] private int initialPoolSize = 10;
    [SerializeField] private int maxPoolSize = 20;

    // 2. 使用官方IObjectPool<T>作为核心存储
    private IObjectPool<GameObject> _pool;

    // 3. 将对象池设置为公共属性，方便外部访问和PoolablePlayer组件回调
    public IObjectPool<GameObject> Pool
    {
        get
        {
            // 延迟初始化，确保在第一次访问时创建对象池
            if (_pool == null)
            {
                _pool = new ObjectPool<GameObject>(
                    OnCreatePoolObject,
                    OnTakeFromPool,
                    OnReturnedToPool,
                    OnDestroyPoolObject,
                    true,           // 开启集合检查，防止对象被重复释放
                    initialPoolSize,
                    maxPoolSize
                );
            }
            return _pool;
        }
    }

    void Start()
    {
        // 可选：在启动时预热对象池
        WarmupPool();
    }

    #region --- Pool Lifecycle Callbacks ---

    // 4. 重构生命周期方法以匹配ObjectPool的委托签名
    private GameObject OnCreatePoolObject()
    {
        if (playerPrefab == null)
        {
            Debug.LogError("[PlayerObjectPool] Player prefab is not assigned!");
            return null;
        }
        GameObject playerObj = Instantiate(playerPrefab);
        
        // 确保新创建的对象有关联的PoolablePlayer组件
        var poolableComponent = playerObj.GetComponent<PoolablePlayer>();
        if (poolableComponent == null)
        {
            poolableComponent = playerObj.AddComponent<PoolablePlayer>();
        }
        poolableComponent.Pool = this.Pool; // 传递IObjectPool引用

        return playerObj;
    }

    private void OnTakeFromPool(GameObject playerObj)
    {
        playerObj.SetActive(true);
        ResetPlayerState(playerObj); // 重置状态
        PoolManager.Instance?.OnObjectIssued(playerObj);
    }

    private void OnReturnedToPool(GameObject playerObj)
    {
        CleanupPlayerState(playerObj); // 清理状态
        playerObj.SetActive(false);
        PoolManager.Instance?.OnObjectReturned(playerObj);
    }

    private void OnDestroyPoolObject(GameObject playerObj)
    {
        PoolManager.Instance?.OnObjectDestroyed(playerObj);
        Destroy(playerObj);
    }

    #endregion

    #region --- Public API ---

    // 5. 简化公共API，直接调用官方对象池的方法
    public GameObject Get()
    {
        return Pool.Get();
    }

    public void Release(GameObject playerObj)
    {
        Pool.Release(playerObj);
    }

    public GameObject GetPlayer(Vector3 position, Quaternion rotation)
    {
        var playerObj = Get();
        if (playerObj != null)
        {
            playerObj.transform.position = position;
            playerObj.transform.rotation = rotation;
            PoolManager.Instance?.RecordPoolAccess("Player", "Get", Pool.CountInactive);
        }
        return playerObj;
    }

    public void ReturnPlayer(GameObject playerObj)
    {
        Release(playerObj);
    }

    public void ClearPool()
    {
        Pool.Clear();
    }

    #endregion

    #region --- Helper Methods ---

    private void WarmupPool()
    {
        var tempObjects = new System.Collections.Generic.List<GameObject>();
        for (int i = 0; i < initialPoolSize; i++)
        {
            tempObjects.Add(Get());
        }
        foreach (var obj in tempObjects)
        {
            Release(obj);
        }
    }

    private void ResetPlayerState(GameObject playerObj)
    {
        playerObj.transform.position = Vector3.zero;
        playerObj.transform.rotation = Quaternion.identity;

        var rb = playerObj.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        var healthComponent = playerObj.GetComponent<HealthComponent>();
        if (healthComponent != null)
        {
            healthComponent.ResetHealth();
        }
    }

    private void CleanupPlayerState(GameObject playerObj)
    {
        // 可在此处添加清理逻辑，例如停止粒子效果
    }

    #endregion
}

/// <summary>
/// 可池化的玩家组件 - 标记对象来自对象池
/// </summary>
public class PoolablePlayer : MonoBehaviour
{
    // 6. Pool的类型现在是官方的IObjectPool
    public IObjectPool<GameObject> Pool { get; set; }

    // OnDisable不再需要，因为对象的返回时机由使用者通过调用Release来控制
    // 可以在此添加一个方法来方便地释放自己
    public void ReleaseToPool()
    {
        if (Pool != null)
        {
            Pool.Release(this.gameObject);
        }
        else
        {
            // 如果没有池，作为后备方案直接销毁
            Destroy(this.gameObject);
        }
    }
}

// --- 辅助类定义 ---
// 为了让脚本能独立编译，保留必要的辅助类定义。
// 在您的项目中，如果这些类在其他文件，可以移除这里的重复定义。

/// <summary>
/// 对象池管理器 - 全局对象池统计和监控
/// </summary>
public class PoolManager : Singleton<PoolManager>
{
    private System.Collections.Generic.Dictionary<string, PoolAccessRecord> _poolAccessRecords = new System.Collections.Generic.Dictionary<string, PoolAccessRecord>();

    protected override void Awake()
    {
        base.Awake();
        DontDestroyOnLoad(gameObject);
    }

    public void RecordPoolAccess(string poolName, string operation, int poolSize)
    {
        string key = $"{poolName}_{operation}";
        if (!_poolAccessRecords.TryGetValue(key, out var record))
        {
            record = new PoolAccessRecord();
            _poolAccessRecords[key] = record;
        }

        record.AccessCount++;
        record.LastAccessTime = Time.time;
        record.CurrentPoolSize = poolSize;
    }

    public void OnObjectIssued(GameObject obj) { }
    public void OnObjectReturned(GameObject obj) { }
    public void OnObjectDestroyed(GameObject obj) { }
}

public class PoolAccessRecord
{
    public int AccessCount = 0;
    public float LastAccessTime = 0f;
    public int CurrentPoolSize = 0;
}

public class HealthComponent : MonoBehaviour
{
    public void ResetHealth() { /* Reset health logic */ }
}

public abstract class Singleton<T> : MonoBehaviour where T : Singleton<T>
{
    private static T _instance;
    public static T Instance => _instance;

    protected virtual void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this as T;
    }
}

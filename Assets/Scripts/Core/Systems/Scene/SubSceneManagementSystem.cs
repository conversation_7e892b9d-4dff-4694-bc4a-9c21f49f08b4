using Unity.Entities;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using Unity.Scenes;
using UnityEngine;
using UnityEngine.SceneManagement;
using MyGame.Core;

/// <summary>
/// DOTS子场景管理系统 - 完全替代传统SceneManager
/// 实现异步场景加载、子场景管理和性能优化
/// </summary>
[UpdateInGroup(typeof(LateSimulationSystemGroup))]
[BurstCompile]
public partial struct SubSceneManagementSystem : ISystem
{
    private NativeHashMap<FixedString128Bytes, Entity> loadedScenes;
    private NativeList<SceneLoadRequest> pendingLoadRequests;
    private EntityQuery cleanupRequestQuery;

    /// <summary>
    /// 系统创建时调用
    /// </summary>
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        loadedScenes = new NativeHashMap<FixedString128Bytes, Entity>(16, Allocator.Persistent);
        pendingLoadRequests = new NativeList<SceneLoadRequest>(8, Allocator.Persistent);
        cleanupRequestQuery = state.GetEntityQuery(ComponentType.ReadOnly<RequestSceneCleanupTag>());

        // 创建场景管理配置实体
        var configEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponentData(configEntity, new SceneManagementConfig
        {
            MaxConcurrentLoads = 3,
            SceneUnloadDelay = 2.0f,
            PreloadDistance = 100.0f,
            IsInitialized = false
        });

        Debug.Log("🏗️ DOTS SubScene Management System initialized");
    }

    /// <summary>
    /// 系统更新时调用
    /// </summary>
    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var deltaTime = SystemAPI.Time.DeltaTime;

        // 处理场景加载请求
        ProcessLoadRequests(ref state);

        // 更新场景状态
        UpdateSceneStates(ref state, deltaTime);

        // 执行流式加载和卸载
        PerformStreamingOperations(ref state);

        // Check for a cleanup request and execute if present
        if (!cleanupRequestQuery.IsEmpty)
        {
            CleanupUnusedScenes(ref state, deltaTime);
            
            // Destroy the request entity to prevent re-triggering
            var ecb = new EntityCommandBuffer(Allocator.Temp);
            ecb.DestroyEntity(cleanupRequestQuery, EntityQueryCaptureMode.AtPlayback);
            ecb.Playback(state.EntityManager);
            ecb.Dispose();
        }
    }

    /// <summary>
    /// 处理场景加载请求
    /// </summary>
    private void ProcessLoadRequests(ref SystemState state)
    {
        // 从请求队列处理加载请求
        foreach (var request in pendingLoadRequests)
        {
            ProcessSingleLoadRequest(ref state, request);
        }

        pendingLoadRequests.Clear();

        // 处理新的ECS请求
        foreach (var (request, entity) in SystemAPI.Query<SceneLoadRequestComponent>()
            .WithEntityAccess())
        {
            var sceneRequest = new SceneLoadRequest
            {
                SceneName = request.SceneName,
                Priority = request.Priority,
                Position = request.Position,
                RequestTime = SystemAPI.Time.ElapsedTime
            };

            ProcessSingleLoadRequest(ref state, sceneRequest);
            state.EntityManager.DestroyEntity(entity);
        }
    }

    /// <summary>
    /// 处理单个加载请求
    /// </summary>
    private void ProcessSingleLoadRequest(ref SystemState state, SceneLoadRequest request)
    {
        // 检查并发加载限制
        var config = SystemAPI.GetSingleton<SceneManagementConfig>();
        if (GetActiveLoadCount(ref state) >= config.MaxConcurrentLoads)
        {
            // 添加回队列等待
            pendingLoadRequests.Add(request);
            return;
        }

        // 创建子场景实体
        var subSceneEntity = state.EntityManager.CreateEntity();

        // 添加请求的场景组件
        var requestComponent = new RequestSceneLoaded
        {
            // Priority 已被移除
        };

        state.EntityManager.AddComponentData(subSceneEntity, requestComponent);

        // 如果是SubScene asset，添加相应的引用
        // 这里需要根据实际的SubScene资源来设置

        // 记录加载的场景
        loadedScenes.Add(request.SceneName, subSceneEntity);

        Debug.Log($"📦 Starting load for scene: {request.SceneName}");

        // 触发场景特定的初始化逻辑
        TriggerSceneSpecificLogic(ref state, request);
    }

    /// <summary>
    /// 更新场景状态
    /// </summary>
    private void UpdateSceneStates(ref SystemState state, float deltaTime)
    {
        ref var config = ref SystemAPI.GetSingletonRW<SceneManagementConfig>().ValueRW;
        
        // 更新卸载计时器
        config.LastUnloadTime += deltaTime;

        // 检查场景加载完成
        CheckSceneLoadCompletion(ref state, ref config);
    }

    /// <summary>
    /// 执行流式加载和卸载
    /// </summary>
    private void PerformStreamingOperations(ref SystemState state)
    {
        // 基于玩家位置的流式加载
        StreamScenesBasedOnPlayerPosition(ref state);

        // 基于距离的场景卸载
        UnloadDistantScenes(ref state);
    }

    /// <summary>
    /// 基于玩家位置的流式加载
    /// </summary>
    private void StreamScenesBasedOnPlayerPosition(ref SystemState state)
    {
        // 获取玩家位置
        var playerPosition = float3.zero;
        if (SystemAPI.TryGetSingletonEntity<PlayerTag>(out var playerEntity))
        {
            playerPosition = SystemAPI.GetComponent<PositionComponent>(playerEntity).Value;
        }

        var config = SystemAPI.GetSingleton<SceneManagementConfig>();
        var preloadDistance = config.PreloadDistance;

        // 检查哪些场景需要加载
        // 这里需要实现场景位置映射逻辑
        // 例如：根据玩家位置确定需要加载的地图区块
    }

    /// <summary>
    /// 卸载距离远的场景
    /// </summary>
    private void UnloadDistantScenes(ref SystemState state)
    {
        var playerPosition = float3.zero;
        if (SystemAPI.TryGetSingletonEntity<PlayerTag>(out var playerEntity))
        {
            playerPosition = SystemAPI.GetComponent<PositionComponent>(playerEntity).Value;
        }

        var unloadDistance = 150.0f; // 可以配置
        var keysToRemove = new NativeList<FixedString128Bytes>(16, Allocator.Temp);

        foreach (var sceneKvp in loadedScenes)
        {
            var sceneEntity = sceneKvp.Value;
            if (state.EntityManager.Exists(sceneEntity))
            {
                // 计算场景距离玩家的距离
                var sceneDistance = CalculateSceneDistance(sceneEntity, playerPosition, ref state);

                if (sceneDistance > unloadDistance)
                {
                    // 标记为卸载
                    SceneSystem.UnloadScene(state.WorldUnmanaged, sceneEntity);
                    keysToRemove.Add(sceneKvp.Key);

                    Debug.Log($"🗑️ Requesting unload for distant scene: {sceneKvp.Key}");
                }
            }
        }

        // 清理记录
        foreach (var key in keysToRemove)
        {
            loadedScenes.Remove(key);
        }

        keysToRemove.Dispose();
    }

    /// <summary>
    /// 清理未使用的场景
    /// </summary>
    private void CleanupUnusedScenes(ref SystemState state, float deltaTime)
    {
        ref var config = ref SystemAPI.GetSingletonRW<SceneManagementConfig>().ValueRW;
        if (config.LastUnloadTime >= config.SceneUnloadDelay)
        {
            // 执行实际的清理操作
            PerformSceneCleanup(ref state);
            config.LastUnloadTime = 0;
        }
    }

    // 辅助方法
    private void CheckSceneLoadCompletion(ref SystemState state, ref SceneManagementConfig config)
    {
        // 这里可以检查RequestedSceneLoaded组件来确定加载完成
        // 当子场景加载完成后，会自动添加这些组件
    }

    private int GetActiveLoadCount(ref SystemState state)
    {
        var loadQuery = SystemAPI.QueryBuilder().WithAll<RequestSceneLoaded>().Build();
        return loadQuery.CalculateEntityCount();
    }

    [BurstDiscard]
    private void TriggerSceneSpecificLogic(ref SystemState state, SceneLoadRequest request)
    {
        // 根据场景类型触发特定的初始化逻辑
        if (request.SceneName.ToString().Contains("Gameplay"))
        {
            // 触发游戏场景初始化
            TriggerGameplaySceneInit(ref state);
        }
        else if (request.SceneName.ToString().Contains("Menu"))
        {
            // 触发菜单场景初始化
            TriggerMenuSceneInit(ref state);
        }
    }

    private float CalculateSceneDistance(Entity sceneEntity, float3 playerPosition, ref SystemState state)
    {
        // 这里应该根据实际的场景位置数据计算距离
        // 临时返回固定值，需要根据实际实现调整
        return 0.0f;
    }

    private void PerformSceneCleanup(ref SystemState state)
    {
        // 执行场景清理逻辑，如卸载未使用的资源
        Debug.Log("🧹 Performing scene cleanup");
    }

    private void TriggerGameplaySceneInit(ref SystemState state)
    {
        Debug.Log("🎮 Initializing gameplay scene");
    }

    private void TriggerMenuSceneInit(ref SystemState state)
    {
        Debug.Log("📋 Initializing menu scene");
    }

    /// <summary>
    /// 系统销毁时调用
    /// </summary>
    [BurstCompile]
    public void OnDestroy(ref SystemState state)
    {
        loadedScenes.Dispose();
        pendingLoadRequests.Dispose();
    }
}

/// <summary>
/// 场景管理配置组件
/// </summary>
[BurstCompile]
public struct SceneManagementConfig : IComponentData
{
    public int MaxConcurrentLoads;
    public float SceneUnloadDelay;
    public float PreloadDistance;
    public FixedString128Bytes CurrentScene;
    public bool IsInitialized;
    public double LastUnloadTime;
}

/// <summary>
/// 场景加载请求组件
/// </summary>
[BurstCompile]
public struct SceneLoadRequestComponent : IComponentData
{
    public FixedString128Bytes SceneName;
    public int Priority;
    public float3 Position;
}

/// <summary>
/// 场景加载请求结构体
/// </summary>
[BurstCompile]
public struct SceneLoadRequest
{
    public FixedString128Bytes SceneName;
    public int Priority;
    public float3 Position;
    public double RequestTime;
}
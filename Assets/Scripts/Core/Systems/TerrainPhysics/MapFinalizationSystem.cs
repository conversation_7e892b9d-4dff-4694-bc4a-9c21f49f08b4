using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using MyGame.Core.Systems.Jobs;

namespace MyGame.Core
{
    // A tag to signal that all iterative physics simulations are complete.
    public struct MapPhysicsCompletedTag : IComponentData { }

    // A tag added to a new entity to request a scene cleanup on the *next* frame.
    public struct RequestSceneCleanupTag : IComponentData { }

    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(WaterFlowSystem))]
    public partial struct MapFinalizationSystem : ISystem
    {
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<MapGenerationCompleted>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            var ecb = new EntityCommandBuffer(Allocator.Temp);

            // Find the map entity via the singleton.
            if (!SystemAPI.TryGetSingleton<WorldMapSingleton>(out var mapSingleton)) return;
            var mapEntity = mapSingleton.MapEntity;
            
            // Add a tag to signal that physics has completed.
            ecb.AddComponent<MapPhysicsCompletedTag>(mapEntity);

            // Create a new entity with a request for the scene manager to clean up.
            // This ensures the cleanup happens on a subsequent frame.
            var cleanupRequestEntity = ecb.CreateEntity();
            ecb.AddComponent<RequestSceneCleanupTag>(cleanupRequestEntity);

            // This system is now triggered by the MapGenerationCompleted event, which is consumed
            // by FinalizeMapGenerationSystem, so it will only run once per generation cycle.
            // No need to disable the system or remove tags.
            
            ecb.Playback(state.EntityManager);
            ecb.Dispose();
        }
    }
}
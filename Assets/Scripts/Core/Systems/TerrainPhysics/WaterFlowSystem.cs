using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Jobs;
using Unity.Mathematics;
using MyGame.Core.Systems.Jobs;
using UnityEngine; // Required for logging
using static Unity.Mathematics.math;

namespace MyGame.Core
{
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(TalusRuleSystem))] // Ensure water flows on stable ground
    public partial struct WaterFlowSystem : ISystem
    {
        private const int MAX_ITERATIONS = 5;
 
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<MapGenerationCompleted>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            if (!SystemAPI.TryGetSingleton<WorldMapSingleton>(out var mapSingleton)) return;
            var mapEntity = mapSingleton.MapEntity;
            var mapComponent = SystemAPI.GetComponent<MapComponent>(mapEntity);
            var tileBuffer = SystemAPI.GetBuffer<TileData>(mapEntity);
            
            var job = new WaterFlowIterationJob
            {
                MapSize = new int2(mapComponent.Width, mapComponent.Height),
                TileBuffer = tileBuffer,
                MaxIterations = MAX_ITERATIONS
            };

            state.Dependency = job.Schedule(state.Dependency);
        }
    }

    [BurstCompile]
    public partial struct WaterFlowIterationJob : IJob
    {
        public int2 MapSize;
        public DynamicBuffer<TileData> TileBuffer;
        [ReadOnly] public int MaxIterations;

        public void Execute()
        {
            var tileArray = TileBuffer.AsNativeArray();
            var waterOutflow = new NativeArray<float>(tileArray.Length, Allocator.Temp);
            var waterInflow = new NativeArray<float>(tileArray.Length, Allocator.Temp);

            for (int i = 0; i < MaxIterations; i++)
            {
                // 1. Calculate Outflow
                for (int index = 0; index < tileArray.Length; index++)
                {
                    int2 pos = new int2(index % MapSize.x, index / MapSize.x);
                    var ourTile = tileArray[index];
                    float ourHeight = csum(asfloat(ourTile.VertexHeights)) * 0.25f;
                    float ourWaterLevel = ourTile.WaterLevel;
                    float ourTotalHeight = ourHeight + ourWaterLevel;
                    float totalFlowOut = 0;

                    for (int y = -1; y <= 1; y++)
                    {
                        for (int x = -1; x <= 1; x++)
                        {
                            if (x == 0 && y == 0) continue;
                            int2 neighborPos = pos + new int2(x, y);
                            if (all(neighborPos >= 0) && all(neighborPos < MapSize))
                            {
                                int neighborIndex = neighborPos.y * MapSize.x + neighborPos.x;
                                var neighborTile = tileArray[neighborIndex];
                                float neighborHeight = csum(asfloat(neighborTile.VertexHeights)) * 0.25f;
                                float neighborTotalHeight = neighborHeight + neighborTile.WaterLevel;
                                float heightDiff = ourTotalHeight - neighborTotalHeight;
                                if (heightDiff > 0)
                                {
                                    totalFlowOut += heightDiff * 0.1f;
                                }
                            }
                        }
                    }
                    waterOutflow[index] = min(totalFlowOut, ourWaterLevel);
                }

                // 2. Distribute Inflow
                for (int j = 0; j < waterInflow.Length; j++)
                {
                    waterInflow[j] = 0;
                }
                for (int index = 0; index < tileArray.Length; index++)
                {
                    float outflow = waterOutflow[index];
                    if (outflow <= 0) continue;

                    int2 pos = new int2(index % MapSize.x, index / MapSize.x);
                    var ourTile = tileArray[index];
                    float ourHeight = csum(asfloat(ourTile.VertexHeights)) * 0.25f;
                    float ourTotalHeight = ourHeight + ourTile.WaterLevel;
                    float totalHeightDiff = 0;

                    for (int y = -1; y <= 1; y++)
                    {
                        for (int x = -1; x <= 1; x++)
                        {
                            if (x == 0 && y == 0) continue;
                            int2 neighborPos = pos + new int2(x, y);
                            if (all(neighborPos >= 0) && all(neighborPos < MapSize))
                            {
                                int neighborIndex = neighborPos.y * MapSize.x + neighborPos.x;
                                var neighborTile = tileArray[neighborIndex];
                                float neighborHeight = csum(asfloat(neighborTile.VertexHeights)) * 0.25f;
                                float neighborTotalHeight = neighborHeight + neighborTile.WaterLevel;
                                float heightDiff = ourTotalHeight - neighborTotalHeight;
                                if (heightDiff > 0)
                                {
                                    totalHeightDiff += heightDiff;
                                }
                            }
                        }
                    }
                    if (totalHeightDiff <= 0) continue;
                    
                    for (int y = -1; y <= 1; y++)
                    {
                        for (int x = -1; x <= 1; x++)
                        {
                            if (x == 0 && y == 0) continue;
                            int2 neighborPos = pos + new int2(x, y);
                            if (all(neighborPos >= 0) && all(neighborPos < MapSize))
                            {
                                int neighborIndex = neighborPos.y * MapSize.x + neighborPos.x;
                                var neighborTile = tileArray[neighborIndex];
                                float neighborHeight = csum(neighborTile.VertexHeights) * 0.25f;
                                float neighborTotalHeight = neighborHeight + neighborTile.WaterLevel;
                                float heightDiff = ourTotalHeight - neighborTotalHeight;
                                if (heightDiff > 0)
                                {
                                    float inflow = (heightDiff / totalHeightDiff) * outflow;
                                    waterInflow[neighborIndex] += inflow;
                                }
                            }
                        }
                    }
                }

                // 3. Apply Changes
                for (int index = 0; index < tileArray.Length; index++)
                {
                    var tile = tileArray[index];
                    tile.WaterLevel += waterInflow[index] - waterOutflow[index];
                    tile.WaterLevel = max(0, tile.WaterLevel);
                    tileArray[index] = tile;
                }
            }

            waterOutflow.Dispose();
            waterInflow.Dispose();
        }
    }
}
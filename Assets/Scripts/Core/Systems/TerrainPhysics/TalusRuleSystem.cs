using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Jobs;
using Unity.Mathematics;
using MyGame.Core.Systems.Jobs;
using UnityEngine; // Required for logging
using static Unity.Mathematics.math;

namespace MyGame.Core
{
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    public partial struct TalusRuleSystem : ISystem
    {
        private const float TALUS_ANGLE = 0.8f; // "安息角"，坡度阈值
        private const int MAX_ITERATIONS = 5; // 为防止死循环或长时间计算，设置最大迭代次数
 
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<MapGenerationCompleted>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            if (!SystemAPI.TryGetSingleton<WorldMapSingleton>(out var mapSingleton)) return;
            var mapEntity = mapSingleton.MapEntity;
            var mapComponent = SystemAPI.GetComponent<MapComponent>(mapEntity);
            var tileBuffer = SystemAPI.GetBuffer<TileData>(mapEntity);
            
            var job = new TalusIterationJob
            {
                MapSize = new int2(mapComponent.Width, mapComponent.Height),
                TileBuffer = tileBuffer,
                TalusAngle = TALUS_ANGLE,
                MaxIterations = MAX_ITERATIONS
            };

            state.Dependency = job.Schedule(state.Dependency);
        }
    }

    [BurstCompile]
    public partial struct TalusIterationJob : IJob
    {
        public int2 MapSize;
        public DynamicBuffer<TileData> TileBuffer;
        [ReadOnly] public float TalusAngle;
        [ReadOnly] public int MaxIterations;

        public void Execute()
        {
            var tileArray = TileBuffer.AsNativeArray();
            var heightChanges = new NativeArray<float4>(tileArray.Length, Allocator.Temp);

            for (int i = 0; i < MaxIterations; i++)
            {
                // Calculation pass
                for (int index = 0; index < tileArray.Length; index++)
                {
                    int2 pos = new int2(index % MapSize.x, index / MapSize.x);
                    float4 ourHeights = asfloat(tileArray[index].VertexHeights);
                    float4 totalHeightChange = new float4(0, 0, 0, 0);

                    for (int y = -1; y <= 1; y++)
                    {
                        for (int x = -1; x <= 1; x++)
                        {
                            if (x == 0 && y == 0) continue;
                            int2 neighborPos = pos + new int2(x, y);
                            if (all(neighborPos >= 0) && all(neighborPos < MapSize))
                            {
                                int neighborIndex = neighborPos.y * MapSize.x + neighborPos.x;
                                float4 neighborHeights = asfloat(tileArray[neighborIndex].VertexHeights);
                                float ourAvgHeight = csum(ourHeights) * 0.25f;
                                float neighborAvgHeight = csum(neighborHeights) * 0.25f;
                                float diff = ourAvgHeight - neighborAvgHeight;
                                if (diff > TalusAngle)
                                {
                                    float amountToMove = (diff - TalusAngle) * 0.5f;
                                    totalHeightChange -= amountToMove;
                                }
                            }
                        }
                    }
                    heightChanges[index] = totalHeightChange;
                }

                // Application pass
                for (int index = 0; index < tileArray.Length; index++)
                {
                    var tile = tileArray[index];
                    float4 currentHeights = asfloat(tile.VertexHeights);
                    currentHeights += heightChanges[index];
                    tile.VertexHeights = asuint(currentHeights);
                    tileArray[index] = tile;
                }
            }
            heightChanges.Dispose();
        }
    }
}
{"name": "Galaxy.MapleLeaf.Core.PlayerSystems", "rootNamespace": "", "references": ["Core", "Unity.Entities", "Unity.Burst", "Unity.Collections", "Unity.Mathematics", "Unity.Transforms", "Unity.InputSystem"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}
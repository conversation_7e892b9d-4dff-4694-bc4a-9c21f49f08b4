using Unity.Entities;
using Unity.Burst;
using Unity.Mathematics;
using Unity.Transforms;

/// <summary>
/// 玩家移动系统 (简化版)
/// 为经典的肉鸽式网格移动预留的模板。
/// </summary>
[UpdateInGroup(typeof(SimulationSystemGroup))]
[BurstCompile]
public partial struct PlayerMovementSystem : ISystem
{
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<PlayerTag>();
        state.RequireForUpdate<PlayerInputComponent>();
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        new MovementJob
        {
            DeltaTime = SystemAPI.Time.DeltaTime
        }.ScheduleParallel();
    }

    [BurstCompile]
    public void OnDestroy(ref SystemState state) { }
}

[WithAll(typeof(PlayerTag))]
[BurstCompile]
public partial struct MovementJob : IJobEntity
{
    public float DeltaTime;

    public void Execute(PlayerMovementAspect player)
    {
        player.Move(DeltaTime);
    }
}
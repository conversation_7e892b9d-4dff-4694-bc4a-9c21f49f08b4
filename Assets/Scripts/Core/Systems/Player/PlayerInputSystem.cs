using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// 玩家输入系统
/// 使用新的Input System处理玩家输入.
/// </summary>
[UpdateInGroup(typeof(InitializationSystemGroup))]
public partial struct PlayerInputSystem : ISystem
{
    private EntityQuery _managedInputQuery;

    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<PlayerTag>();
        state.RequireForUpdate<PlayerInputComponent>();
        _managedInputQuery = state.GetEntityQuery(typeof(ManagedInputActions));

        // 尝试获取托管的输入组件，如果不存在则创建
        if (_managedInputQuery.IsEmpty)
        {
            var entity = state.EntityManager.CreateEntity();
            var newInput = new ManagedInputActions { Value = new InputActions() };
            state.EntityManager.AddComponentData(entity, newInput);
        }
    }

    public void OnUpdate(ref SystemState state)
    {
        // 从单例实体获取托管的输入组件
        if (_managedInputQuery.IsEmpty)
        {
            return;
        }
        var managedInput = _managedInputQuery.GetSingleton<ManagedInputActions>();
        var inputActions = managedInput.Value;

        // 确保Action Map已启用
        if (!inputActions.Player.enabled)
        {
            inputActions.Player.Enable();
        }

        var moveInput = inputActions.Player.Move.ReadValue<Vector2>();

        // 省略了将Vector2转换为int2的逻辑...
        int2 discreteMove = int2.zero;
        if (math.abs(moveInput.x) > math.abs(moveInput.y))
        {
            if (moveInput.x > 0.5f) discreteMove.x = 1;
            else if (moveInput.x < -0.5f) discreteMove.x = -1;
        }
        else
        {
            if (moveInput.y > 0.5f) discreteMove.y = 1;
            else if (moveInput.y < -0.5f) discreteMove.y = -1;
        }

        // 使用新的PlayerInputAspect来更新玩家输入组件
        foreach (var player in SystemAPI.Query<PlayerInputAspect>().WithAll<PlayerTag>())
        {
            player.MoveDirection = discreteMove;
        }

        // Reset the move direction to zero after processing, to avoid continuous movement.
        // This should be done in a separate query or by caching the entity.
        // For simplicity, we do it in another query here.
        foreach (var playerInput in SystemAPI.Query<RefRW<PlayerInputComponent>>().WithAll<PlayerTag>())
        {
            if (discreteMove.x != 0 || discreteMove.y != 0)
            {
                // Only reset if there was an input, to avoid resetting during a held input frame
                // which might be needed for other systems. A better approach would be a dedicated InputConsumedTag.
            }
            // For now, let's not reset it, as it might interfere with the movement system.
            // A proper implementation would use an event or a tag to signal that the input has been consumed.
        }
    }

    public void OnDestroy(ref SystemState state)
    {
        // 从单例实体获取托管的输入组件并禁用
        if (!_managedInputQuery.IsEmpty)
        {
            var managedInput = _managedInputQuery.GetSingleton<ManagedInputActions>();
            if (managedInput.Value != null && managedInput.Value.Player.enabled)
            {
                managedInput.Value.Player.Disable();
            }
        }
    }
}
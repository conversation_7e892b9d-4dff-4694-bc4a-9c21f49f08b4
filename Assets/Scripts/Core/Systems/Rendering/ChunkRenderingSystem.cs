using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Rendering;

namespace MyGame.Core
{
    [BurstCompile]
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    [UpdateAfter(typeof(PlayerChunkSystem))]
    public partial struct ChunkRenderingSystem : ISystem
    {
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // This system should run if we have a player and chunks to render.
            state.RequireForUpdate<CurrentPlayerChunkComponent>();
            state.RequireForUpdate<ChunkComponent>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            if (!SystemAPI.TryGetSingleton<CurrentPlayerChunkComponent>(out var currentPlayerChunk))
            {
                return;
            }

            // Using a Temp ECB as we will play it back immediately this frame.
            var ecb = new EntityCommandBuffer(Allocator.Temp);

            var job = new UpdateChunkVisibilityJob
            {
                ECB = ecb.AsParallelWriter(),
                CurrentPlayerChunkPos = currentPlayerChunk.Value,
                RenderDistance = 2 // Render a 5x5 chunk area (current + 2 chunks in each direction)
            };
            
            // Schedule the job to run over all entities that have a ChunkComponent and MMI
            state.Dependency = job.ScheduleParallel(state.Dependency);

            // Add the ECB playback to the dependency chain
            state.Dependency.Complete();
            ecb.Playback(state.EntityManager);
            ecb.Dispose();
        }

        [BurstCompile]
        [WithAll(typeof(MaterialMeshInfo))] // Only process entities that are renderable
        public partial struct UpdateChunkVisibilityJob : IJobEntity
        {
            public EntityCommandBuffer.ParallelWriter ECB;
            [ReadOnly] public int2 CurrentPlayerChunkPos;
            [ReadOnly] public int RenderDistance;

            // The job now correctly executes on entities with a ChunkComponent
            void Execute(Entity entity, [ChunkIndexInQuery] int chunkIndex, in ChunkComponent chunk)
            {
                int2 distance = math.abs(chunk.ChunkPosition - CurrentPlayerChunkPos);
                bool isVisible = math.all(distance <= RenderDistance);
                
                // Enable or disable the MaterialMeshInfo component to show/hide the chunk
                ECB.SetComponentEnabled<MaterialMeshInfo>(chunkIndex, entity, isVisible);
            }
        }
    }
}
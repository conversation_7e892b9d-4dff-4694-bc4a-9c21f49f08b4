using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Rendering;
using MyGame.Core.Systems.Jobs;
using MyGame.Core.Systems.Diagnostics;
using Unity.Transforms;
using Unity.Mathematics;

namespace MyGame.Core
{
    [BurstCompile]
    [UpdateInGroup(typeof(LateSimulationSystemGroup))]
    public partial struct AsyncTileMeshGenerationSystem : ISystem
    {
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // REMOVED: This was causing a deadlock because the Baker was not creating a singleton correctly.
            // The check for the buffer's existence is now properly handled in OnUpdate.
            // state.RequireForUpdate<TileTypeMaterial>();

            // The system will run as long as there are Chunks without MaterialMeshInfo.
            state.RequireForUpdate<ChunkComponent>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // This system is now event-driven, but we still consume the event in FinalizeMapGenerationSystem.
            // The RequireForUpdate is sufficient.

            var logBuffer = SystemAPI.GetSingletonBuffer<LogMessage>();

            var tileTypeMaterialBuffer = SystemAPI.GetSingletonBuffer<TileTypeMaterial>();
            if (tileTypeMaterialBuffer.IsEmpty) return;

            var ecbSingleton = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

            var tileTypeLookup = new NativeHashMap<int, MaterialMeshInfo>(tileTypeMaterialBuffer.Length, Allocator.TempJob);
            foreach (var item in tileTypeMaterialBuffer)
            {
                tileTypeLookup.Add((int)item.Key, item.Value);
            }
            
            // CORRECTED QUERY: We are looking for CHUNKS that need meshes, not tiles.
            var query = SystemAPI.QueryBuilder().WithAll<ChunkComponent>().Build();
            logBuffer.Add(new LogMessage { Value = $"[AsyncTileMeshGen] Found {query.CalculateEntityCount()} new CHUNKS to process." });

            var job = new AddRenderMeshToChunkJob
            {
                ECB = ecb.AsParallelWriter(),
                // A simple placeholder MMI. In a real scenario, we'd generate a mesh for the chunk.
                // For now, we'll give every chunk a default "grass" mesh to make it visible.
                DefaultMMI = tileTypeLookup.ContainsKey((int)TileType.Floor) ? tileTypeLookup[(int)TileType.Floor] : new MaterialMeshInfo()
            };

            state.Dependency = job.ScheduleParallel(query, state.Dependency);
            
            // Dispose the temp hash map
            state.Dependency = tileTypeLookup.Dispose(state.Dependency);
        }

        [BurstCompile]
        public partial struct AddRenderMeshToChunkJob : IJobEntity
        {
            public EntityCommandBuffer.ParallelWriter ECB;
            public MaterialMeshInfo DefaultMMI;

            // CORRECTED JOB: Execute on each CHUNK entity
            void Execute(Entity entity, [ChunkIndexInQuery] int chunkIndex, in ChunkComponent chunk)
            {
                // 1. Add the rendering mesh
                ECB.AddComponent(chunkIndex, entity, DefaultMMI);

                // 2. Add the LocalToWorld transform component - THE MISSING PIECE!
                var transform = new LocalToWorld
                {
                    Value = float4x4.TRS(
                        new float3(chunk.WorldPosition.x, 0, chunk.WorldPosition.y), // Use chunk's world position
                        quaternion.identity,
                        1.0f
                    )
                };
                ECB.AddComponent(chunkIndex, entity, transform);
            }
        }
    }
}
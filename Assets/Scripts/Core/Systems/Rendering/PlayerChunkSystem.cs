using Unity.Burst;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using System.Collections.Generic;
using MyGame.Core;

namespace MyGame.Core
{
    [BurstCompile]
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial struct PlayerChunkSystem : ISystem
    {
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<PlayerTag>();
            state.RequireForUpdate<MapComponent>();
            state.RequireForUpdate<CurrentPlayerChunkComponent>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            if (!SystemAPI.TryGetSingletonEntity<PlayerTag>(out var playerEntity))
                return;

            var mapComponent = SystemAPI.GetSingleton<MapComponent>();
            if (mapComponent.ChunkSize.x == 0 || mapComponent.ChunkSize.y == 0)
                return;

            var playerTransform = SystemAPI.GetComponent<LocalTransform>(playerEntity);
            var playerPosition = playerTransform.Position;

            var chunkSize = new float2(mapComponent.ChunkSize.x, mapComponent.ChunkSize.y);
            var currentChunkPos = (int2)math.floor(playerPosition.xz / chunkSize);

            var currentPlayerChunk = SystemAPI.GetSingletonRW<CurrentPlayerChunkComponent>();

            if (!currentPlayerChunk.ValueRO.Value.Equals(currentChunkPos))
            {
                currentPlayerChunk.ValueRW.Value = currentChunkPos;
            }
        }
    }
}
using MyGame.Core;
using MyGame.Core.Systems.Jobs;
using Unity.Entities;
using Unity.Collections;
using Unity.Mathematics;
using System.Collections.Generic;
using UnityEngine.InputSystem;

/// <summary>
/// 托管组件，用于在DOTS世界中存储非托管的InputActions实例。
/// 这样ISystem就可以通过查询这个组件来访问它。
/// </summary>
public class ManagedInputActions : IComponentData
{
    public InputActions Value;
}

/// <summary>
/// UI事件系统 - DOTS驱动的UI事件处理
/// 替代传统UGUI事件系统，实现数据驱动的UI状态管理
/// </summary>
[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial struct UIEventSystem : ISystem
{
    private EntityQuery _managedInputQuery;
    
    /// <summary>
    /// 系统创建时调用
    /// </summary>
    public void OnCreate(ref SystemState state)
    {
        // 创建UI配置实体
        var uiConfigEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponentData(uiConfigEntity, new UIConfigComponent
        {
            ActiveViews = new FixedString512Bytes(""),
            LastEventTime = 0,
            EventQueueCapacity = 16
        });

        // 创建事件队列缓冲区
        state.EntityManager.AddBuffer<UIEventBufferElement>(uiConfigEntity);
        
        _managedInputQuery = state.GetEntityQuery(typeof(ManagedInputActions));

        // 复用PlayerInputSystem创建的ManagedInputActions单例
        if (_managedInputQuery.IsEmpty)
        {
            var entity = state.EntityManager.CreateEntity();
            state.EntityManager.AddComponentData(entity, new ManagedInputActions { Value = new @InputActions() });
        }
        
        UnityEngine.Debug.Log("🎨 UI System initialized via DOTS");
    }

    /// <summary>
    /// 系统更新时调用
    /// </summary>
    public void OnUpdate(ref SystemState state)
    {
        if (_managedInputQuery.IsEmpty) return;
        var managedInput = _managedInputQuery.GetSingleton<ManagedInputActions>();
        var inputActions = managedInput.Value;

        if (!inputActions.UI.enabled)
        {
            inputActions.UI.Enable();
        }

        foreach (var uiConfigAspect in SystemAPI.Query<UIConfigAspect>())
        {
            // 处理UI事件队列
            ProcessUIEvents(ref state, uiConfigAspect);

            // 更新UI状态
            var deltaTime = SystemAPI.Time.DeltaTime;
            var currentTime = SystemAPI.Time.ElapsedTime;
            UpdateUIStates(ref state, deltaTime, currentTime, uiConfigAspect);

            // 处理输入到UI的映射
            ProcessInputToUI(ref state, inputActions);

            // Since this is a singleton, we only need to run this once.
            break;
        }
    }
    
    public void OnDestroy(ref SystemState state)
    {
        // InputActions的生命周期由ManagedInputActions单例管理，此处无需处理
    }

    /// <summary>
    /// 处理UI事件队列
    /// </summary>
    private void ProcessUIEvents(ref SystemState state, UIConfigAspect uiConfigAspect)
    {
        for (int i = 0; i < uiConfigAspect.EventBuffer.Length; i++)
        {
            var uiEvent = uiConfigAspect.EventBuffer[i];
            ProcessSingleUIEvent(ref state, uiEvent, uiConfigAspect);

            // 更新时间戳
            uiConfigAspect.LastEventTime = SystemAPI.Time.ElapsedTime;
        }

        // 清空已处理的事件
        uiConfigAspect.EventBuffer.Clear();
    }

    /// <summary>
    /// 处理单个UI事件
    /// </summary>
    private void ProcessSingleUIEvent(ref SystemState state, UIEventBufferElement uiEvent, UIConfigAspect uiConfigAspect)
    {
        switch (uiEvent.EventType)
        {
            case UIEventType.ButtonPressed:
                HandleButtonPress(ref state, uiEvent);
                break;
            case UIEventType.ViewOpened:
                HandleViewOpen(ref state, uiEvent, uiConfigAspect);
                break;
            case UIEventType.ViewClosed:
                HandleViewClose(ref state, uiEvent, uiConfigAspect);
                break;
            case UIEventType.ValueChanged:
                HandleValueChange(ref state, uiEvent);
                break;

            // Core -> UI Events
            case UIEventType.PlayerDataUpdated:
            case UIEventType.MapDataUpdated:
            case UIEventType.GameStateUpdated:
                ForwardEventToOutbox(uiEvent);
                break;
        }
    }

    /// <summary>
    /// 处理按钮按下事件
    /// </summary>
    private void HandleButtonPress(ref SystemState state, UIEventBufferElement uiEvent)
    {
        // 根据按钮ID执行相应的动作
        switch (uiEvent.EventData.ButtonId.Value)
        {
            case var id when id.Contains("GenerateMap"):
                // 触发地图生成
                TriggerMapGeneration(ref state);
                break;
            case var id when id.Contains("PauseGame"):
                // 暂停游戏
                TriggerGamePause();
                break;
            case var id when id.Contains("QuitGame"):
                // 退出游戏
                TriggerGameQuit();
                break;
        }

        UnityEngine.Debug.Log($"🔘 UI Button pressed: {uiEvent.EventData.ButtonId}");
    }

    /// <summary>
    /// 处理视图打开事件
    /// </summary>
    private void HandleViewOpen(ref SystemState state, UIEventBufferElement uiEvent, UIConfigAspect uiConfigAspect)
    {
        // 更新活跃视图列表
        uiConfigAspect.ActiveViews = uiEvent.EventData.ViewName;
        UnityEngine.Debug.Log($"📂 UI View opened: {uiEvent.EventData.ViewName}");
    }

    /// <summary>
    /// 处理视图关闭事件
    /// </summary>
    private void HandleViewClose(ref SystemState state, UIEventBufferElement uiEvent, UIConfigAspect uiConfigAspect)
    {
        // 清理活跃视图列表
        uiConfigAspect.ActiveViews = new FixedString512Bytes("");
        UnityEngine.Debug.Log($"📁 UI View closed: {uiEvent.EventData.ViewName}");
    }

    /// <summary>
    /// 处理值改变事件
    /// </summary>
    private void HandleValueChange(ref SystemState state, UIEventBufferElement uiEvent)
    {
        // 处理滑块、输入框等控件的值改变
        UpdateConfigurationValue(uiEvent);
        UnityEngine.Debug.Log($"📊 UI Value changed: {uiEvent.EventData.ControlId} = {uiEvent.EventData.FloatValue}");
    }

    /// <summary>
    /// 将一个事件转发到UI发件箱，供UI层轮询。
    /// </summary>
    private void ForwardEventToOutbox(UIEventBufferElement uiEvent)
    {
        UIEventOutbox.Events.Enqueue(uiEvent);
    }

    /// <summary>
    /// 更新UI状态
    /// </summary>
    private void UpdateUIStates(ref SystemState state, float deltaTime, double currentTime, UIConfigAspect uiConfigAspect)
    {
        // 检查界面超时或其他状态更新逻辑
        UpdateUIStateLogic(uiConfigAspect, deltaTime, currentTime);
    }

    /// <summary>
    /// 处理输入到UI的映射
    /// </summary>
    private void ProcessInputToUI(ref SystemState state, InputActions inputActions)
    {
        if (inputActions.UI.Cancel.WasPressedThisFrame())
        {
            TriggerUIMenuToggle();
        }

        if (inputActions.UI.ToggleMenu.WasPressedThisFrame())
        {
            TriggerUIFocusChange();
        }
    }

    // 辅助方法
    private void TriggerMapGeneration(ref SystemState state)
    {
        // Check if a generation is already in progress.
        var query = SystemAPI.QueryBuilder().WithAll<JobTrackerComponent>().Build();
        if (!query.IsEmpty)
        {
            UnityEngine.Debug.LogWarning("Map generation is already in progress. Ignoring new request.");
            return;
        }

        UnityEngine.Debug.Log("UIEventSystem: Triggering map generation.");
        var requestEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponent<RequestTerrainPhysics>(requestEntity);
    }

    private void TriggerGamePause()
    {
        UnityEngine.Debug.Log("⏸️ Game paused via UI");
    }

    private void TriggerGameQuit()
    {
        UnityEngine.Debug.Log("🚪 Game quit requested via UI");
    }

    private void UpdateUIStateLogic(UIConfigAspect uiConfigAspect, float deltaTime, double currentTime)
    {
        // 实现UI状态更新的逻辑
    }

    private void UpdateConfigurationValue(UIEventBufferElement uiEvent)
    {
        // 更新对应配置值
    }

    private void TriggerUIMenuToggle()
    {
        UnityEngine.Debug.Log("🔄 UI Menu toggled via keyboard");
    }

    private void TriggerUIFocusChange()
    {
        UnityEngine.Debug.Log("🎯 UI Focus changed via Tab key");
    }
}

/// <summary>
/// UI配置组件
/// </summary>
// [BurstCompile]
public struct UIConfigComponent : IComponentData
{
    public FixedString512Bytes ActiveViews;
    public double LastEventTime;
    public int EventQueueCapacity;
}

/// <summary>
/// UI事件缓冲区元素
/// </summary>
// [BurstCompile]
public struct UIEventBufferElement : IBufferElementData
{
    public UIEventType EventType;
    public UIEventData EventData;
}

/// <summary>
/// UI事件类型枚举
/// </summary>
public enum UIEventType
{
    ButtonPressed,
    ViewOpened,
    ViewClosed,
    ValueChanged,
    FocusChanged,
    MenuOpened,
    MenuClosed,

    // 从Core到UI的事件
    PlayerDataUpdated,
    MapDataUpdated,
    GameStateUpdated
}

/// <summary>
/// UI事件数据
/// </summary>
// [BurstCompile]
public struct UIEventData : IComponentData
{
    // --- UI -> Core 事件数据 ---
    public FixedString128Bytes ButtonId;
    public FixedString128Bytes ViewName;
    public FixedString128Bytes ControlId;
    public float FloatValue;
    public int IntValue;
    public bool BoolValue;
    public double Timestamp;

    // --- Core -> UI 事件数据 ---
    // Player Data
    public int PlayerData_Level;
    public int PlayerData_Experience;
    public int PlayerData_CurrentHealth;
    public int PlayerData_MaxHealth;
    public int PlayerData_Gold;
    public FixedString64Bytes PlayerData_Name;

    // Map Data
    public FixedString64Bytes MapData_MapName;
    public int MapData_Width;
    public int MapData_Height;

    // Game State Data
    public int GameState_Status; // Stored as int for blittable compatibility
    public float GameState_GameTime;
}

// --- 以下是合并的代码 ---

/// <summary>
/// 一个静态的“发件箱”，用于从Core程序集向外部（如UI程序集）传递UI事件数据。
/// UIEventSystem将事件数据写入此队列，而UI层的监听器则从中读取。
/// 这是一种单向数据流，避免了Core对UI的直接依赖。
/// </summary>
public static class UIEventOutbox
{
    /// <summary>
    /// 存储从Core层发往UI层的事件数据队列。
    /// 注意：这不是线程安全的。所有写入和读取都应在主线程上完成。
    /// </summary>
    public static readonly Queue<UIEventBufferElement> Events = new Queue<UIEventBufferElement>();
}

/// <summary>
/// 一个静态代理类，为非System代码（或其他System）提供了一个简单的API来发送UI事件。
/// 它封装了查找UI事件队列并向其中添加事件的逻辑。
/// </summary>
public static class UIEventProxy
{
    /// <summary>
    /// 发送一个UI事件到UI事件系统中。
    /// </summary>
    /// <param name="world">事件应该在哪个World中被发送。</param>
    /// <param name="eventType">事件的类型。</param>
    /// <param name="eventData">事件附带的数据。</param>
    public static void SendEvent(World world, UIEventType eventType, UIEventData eventData)
    {
        if (world == null || !world.IsCreated)
        {
            UnityEngine.Debug.LogError("UIEventProxy: World is null or not created.");
            return;
        }

        var entityManager = world.EntityManager;
        try
        {
            var uiConfigEntity = entityManager.CreateEntityQuery(typeof(UIConfigComponent)).GetSingletonEntity();
            if (uiConfigEntity == Entity.Null)
            {
                UnityEngine.Debug.LogWarning("UIEventProxy: UIConfigComponent entity not found. Has UIEventSystem run?");
                return;
            }

            var eventBuffer = entityManager.GetBuffer<UIEventBufferElement>(uiConfigEntity);
            eventBuffer.Add(new UIEventBufferElement
            {
                EventType = eventType,
                EventData = eventData
            });
        }
        catch (System.InvalidOperationException)
        {
            UnityEngine.Debug.LogWarning("UIEventProxy: UIConfigComponent singleton not found or multiple exist.");
        }
    }

    /// <summary>
    /// 发送一个UI事件到默认的World中。
    /// </summary>
    public static void SendEvent(UIEventType eventType, UIEventData eventData)
    {
        SendEvent(World.DefaultGameObjectInjectionWorld, eventType, eventData);
    }
}

public readonly partial struct UIConfigAspect : IAspect
{
    public readonly Entity Entity;

    private readonly RefRW<UIConfigComponent> _uiConfig;
    private readonly DynamicBuffer<UIEventBufferElement> _eventBuffer;

    public FixedString512Bytes ActiveViews
    {
        get => _uiConfig.ValueRO.ActiveViews;
        set => _uiConfig.ValueRW.ActiveViews = value;
    }

    public double LastEventTime
    {
        get => _uiConfig.ValueRO.LastEventTime;
        set => _uiConfig.ValueRW.LastEventTime = value;
    }
    
    public DynamicBuffer<UIEventBufferElement> EventBuffer => _eventBuffer;
}
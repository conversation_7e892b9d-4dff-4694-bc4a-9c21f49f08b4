using Unity.Entities;
using Unity.Collections;

/// <summary>
/// 监测玩家数据变化，并通过UIEventSystem广播事件的系统。
/// 这个系统与托管代码UIEventProxy交互，因此不能使用Burst编译。
/// </summary>
[UpdateInGroup(typeof(PresentationSystemGroup))]
public partial struct UIDataBridgeSystem : ISystem
{
    private PlayerDataComponent _cachedPlayerData;

    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<PlayerDataComponent>();
        _cachedPlayerData = default; // 初始化缓存
    }

    public void OnDestroy(ref SystemState state)
    {
    }

    public void OnUpdate(ref SystemState state)
    {
        // 获取玩家数据组件的单例
        var currentPlayer = SystemAPI.GetSingleton<PlayerDataComponent>();

        // 与缓存的数据进行比较
        if (currentPlayer.Equals(_cachedPlayerData))
        {
            return; // 数据未变化，不执行任何操作
        }

        // 数据已更改，更新缓存并发送事件
        _cachedPlayerData = currentPlayer;
        
        var eventData = new UIEventData
        {
            PlayerData_Level = currentPlayer.Level,
            PlayerData_Experience = currentPlayer.Experience,
            PlayerData_CurrentHealth = (int)currentPlayer.Health,
            PlayerData_MaxHealth = (int)currentPlayer.MaxHealth,
            PlayerData_Name = currentPlayer.PlayerName,
            PlayerData_Gold = 0, // PlayerDataComponent中没有Gold，暂时设为0
            Timestamp = state.WorldUnmanaged.Time.ElapsedTime
        };
        
        // This system interacts with a managed static class, so it cannot be Burst compiled.
        UIEventProxy.SendEvent(UIEventType.PlayerDataUpdated, eventData);
    }
}
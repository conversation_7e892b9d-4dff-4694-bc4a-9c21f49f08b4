using Unity.Entities;
using Unity.Collections;

namespace MyGame.Core
{
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(UIEventSystem))]
    public partial struct UIMapDataBridgeSystem : ISystem
    {
        private EntityQuery _mapDataQuery;
        private MapDataComponent _cachedMapData;

        public void OnCreate(ref SystemState state)
        {
            _mapDataQuery = state.GetEntityQuery(typeof(MapDataComponent));
            _cachedMapData = default;
        }

        public void OnDestroy(ref SystemState state)
        {
        }

        public void OnUpdate(ref SystemState state)
        {
            if (_mapDataQuery.IsEmpty)
            {
                return;
            }

            var currentMapData = _mapDataQuery.GetSingleton<MapDataComponent>();

            if (currentMapData.Equals(_cachedMapData))
            {
                return; // Data has not changed
            }

            _cachedMapData = currentMapData;

            var eventData = new UIEventData
            {
                MapData_MapName = currentMapData.MapName,
                MapData_Width = currentMapData.Width,
                MapData_Height = currentMapData.Height,
                Timestamp = SystemAPI.Time.ElapsedTime
            };

            // This system interacts with a managed static class, so it cannot be Burst compiled.
            UIEventProxy.SendEvent(UIEventType.MapDataUpdated, eventData);
        }
    }
}
using Unity.Entities;

namespace MyGame.Core
{
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(UIEventSystem))]
    public partial struct UIGameStateBridgeSystem : ISystem
    {
        private EntityQuery _gameStateQuery;
        private GameStateComponent _cachedGameState;

        public void OnCreate(ref SystemState state)
        {
            _gameStateQuery = state.GetEntityQuery(typeof(GameStateComponent));
            _cachedGameState = default;
        }

        public void OnDestroy(ref SystemState state)
        {
        }

        public void OnUpdate(ref SystemState state)
        {
            if (_gameStateQuery.IsEmpty)
            {
                return;
            }

            var currentGameState = _gameStateQuery.GetSingleton<GameStateComponent>();

            if (currentGameState.Equals(_cachedGameState))
            {
                return; // Data has not changed
            }

            _cachedGameState = currentGameState;

            var eventData = new UIEventData
            {
                GameState_Status = (int)currentGameState.Status,
                GameState_GameTime = currentGameState.GameTime,
                Timestamp = SystemAPI.Time.ElapsedTime
            };

            // This system interacts with a managed static class, so it cannot be Burst compiled.
            UIEventProxy.SendEvent(UIEventType.GameStateUpdated, eventData);
        }
    }
}
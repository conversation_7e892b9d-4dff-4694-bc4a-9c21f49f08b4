using Unity.Entities;
using Unity.Burst;
using Unity.Mathematics;
using UnityEngine.Scripting;
using UnityEngine;
using MyGame.Core;
using MyGame.Core.Systems;
using MyGame.Core.Systems.Diagnostics;

/// <summary>
/// 纯DOTS引导系统 - 替代传统MonoBehaviour引导器
/// 使用RuntimeInitializeOnLoadMethod实现无GameObject依赖的初始化
/// </summary>
[BurstCompile]
[UpdateInGroup(typeof(InitializationSystemGroup), OrderFirst = true)]
public partial struct GameBootstrapperSystem : ISystem
{
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        // OnUpdate will run once and then disable itself.
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        Debug.Log("GameBootstrapperSystem triggered. Creating map entity and other global entities.");

        // 创建引导实体用于跟踪状态
        var bootstrapEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponentData(bootstrapEntity, new BootstrapStateComponent
        {
            IsInitialized = true,
            InitializationTime = SystemAPI.Time.ElapsedTime
        });
        
        // --- 触发默认地图初始化 ---
        var mapEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddBuffer<TileData>(mapEntity);
        state.EntityManager.AddBuffer<TreePosition>(mapEntity);
        state.EntityManager.AddComponentData(mapEntity, new MapComponent
        {
            Width = 256,
            Height = 256,
            Seed = 12345,
            ChunkSize = new int2(64, 64),
            ForestDensity = 0.5f,
            Center = new int2(128, 128),
            MapType = MapType.World
        });

        // Create the singleton that holds a reference to the map entity
        var singletonEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponentData(singletonEntity, new WorldMapSingleton
        {
            MapEntity = mapEntity
        });

        // Create the trigger entity for the physics simulation phase
        var physicsTriggerEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponent<RequestTerrainPhysics>(physicsTriggerEntity);
        Debug.Log("Created physics trigger entity with RequestTerrainPhysics.");
        
        // --- 创建全局玩家数据实体 ---
        var playerEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponentData(playerEntity, new PlayerDataComponent
        {
            PlayerId = 1,
            PlayerName = "Player",
            Level = 1,
            Experience = 0,
            Health = 100f,
            MaxHealth = 100f
        });

        // --- 创建全局网络配置实体 ---
        var networkConfigEntity = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponentData(networkConfigEntity, new NetworkConfigComponent
        {
            ServerUrl = "https://jsonplaceholder.typicode.com"
        });

        // --- Create Global Singleton for Buffers ---
        var globalSingleton = state.EntityManager.CreateEntity();
        state.EntityManager.AddComponent<GlobalSingletonTag>(globalSingleton);
        state.EntityManager.AddBuffer<LogMessage>(globalSingleton);

        // 任务完成后禁用自身
        state.Enabled = false;
    }
}

/// <summary>
/// 引导状态组件 - 跟踪引导过程
/// </summary>
[BurstCompile]
public struct BootstrapStateComponent : IComponentData
{
    public bool IsInitialized;
    public double InitializationTime;
}
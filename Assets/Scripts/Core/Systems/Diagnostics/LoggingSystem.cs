using Unity.Entities;
using UnityEngine;

namespace MyGame.Core.Systems.Diagnostics
{
    // This system runs in the PresentationSystemGroup, ensuring it runs on the main thread
    // and after most other systems have completed.
    [UpdateInGroup(typeof(PresentationSystemGroup), OrderLast = true)]
    public partial class LoggingSystem : SystemBase
    {
        private EntityQuery _logQuery;

        protected override void OnCreate()
        {
            // Create a query to find the singleton entity with our log buffer.
            _logQuery = GetEntityQuery(ComponentType.ReadOnly<GlobalSingletonTag>(), ComponentType.ReadWrite<LogMessage>());
            // This system should only run if the singleton with the buffer exists.
            RequireForUpdate(_logQuery);
        }

        protected override void OnUpdate()
        {
            // Get the singleton entity. If it doesn't exist, something is wrong,
            // but the RequireForUpdate above should prevent this from running.
            if (!_logQuery.HasSingleton<GlobalSingletonTag>())
            {
                return;
            }

            var logEntity = _logQuery.GetSingletonEntity();
            var logBuffer = EntityManager.GetBuffer<LogMessage>(logEntity);

            if (logBuffer.IsEmpty)
            {
                return;
            }

            // Copy messages to a native array to avoid issues while iterating and modifying.
            var messages = logBuffer.ToNativeArray(Unity.Collections.Allocator.Temp);
            
            // Log all messages from the buffer.
            foreach (var log in messages)
            {
                Debug.Log(log.Value.ToString());
            }

            // Clear the buffer now that all messages have been logged.
            logBuffer.Clear();
            
            messages.Dispose();
        }
    }
}
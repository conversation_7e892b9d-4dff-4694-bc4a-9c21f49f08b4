using Unity.Collections;
using Unity.Entities;

namespace MyGame.Core.Systems.Diagnostics
{
    /// <summary>
    /// A buffer element to hold a single log message.
    /// This can be added to a singleton entity to allow Burst jobs/systems
    /// to log messages in a thread-safe way.
    /// </summary>
    public struct LogMessage : IBufferElementData
    {
        // Using FixedString is necessary for Burst compatibility.
        // 128 bytes should be enough for most debug messages.
        public FixedString128Bytes Value;
    }

    /// <summary>
    /// A tag component to easily find the singleton entity that holds global buffers.
    /// </summary>
    public struct GlobalSingletonTag : IComponentData { }
}
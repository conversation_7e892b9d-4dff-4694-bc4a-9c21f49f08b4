using Unity.Collections;
using Unity.Jobs;
using Unity.Entities;
using System;
using System.Collections.Generic;
using Unity.Burst;
using UnityEngine;

namespace MyGame.Core.Systems
{
    /// <summary>
    /// 一个非托管句柄，用于安全地从 Burst 编译的系统中引用托管的 SystemBuffer。
    /// </summary>
    public struct SystemBufferHandle<T> where T : unmanaged
    {
        // internal a.k.a assembly, for security.
        internal int Id;
        internal int GenerationId;
    }
    
    // 接口用于类型擦除，以便我们可以在一个列表中存储不同类型的缓冲区
internal interface ISystemBuffer : IDisposable
{
}

// 实际的缓冲区包装器（现在是内部的）
    internal class SystemBuffer<T> : ISystemBuffer where T : unmanaged
    {
        private NativeList<T> _buffer;
    
        public SystemBuffer()
        {
            _buffer = new NativeList<T>(Allocator.Persistent);
        }
    
        public NativeList<T> Get()
        {
            return _buffer;
        }
        
        public void Dispose()
        {
            if (_buffer.IsCreated)
            {
                _buffer.Dispose();
            }
        }
    }

    /// <summary>
    /// 一个静态管理器，用于处理所有 SystemBuffer 的生命周期。
    /// 依赖于 SystemBufferManagerCleanupSystem 在世界销毁时调用 DisposeAll。
    /// </summary>
    public static class SystemBufferManager
    {
        private static readonly List<ISystemBuffer> s_buffers = new();
        private static int s_generationId = 0;

        // 这是一个托管方法，所以它需要被 Burst 丢弃
        [BurstDiscard]
        public static SystemBufferHandle<T> Register<T>() where T : unmanaged
        {
            var buffer = new SystemBuffer<T>();
            s_buffers.Add(buffer);
            return new SystemBufferHandle<T>
            {
                Id = s_buffers.Count - 1,
                GenerationId = s_generationId
            };
        }

        [BurstDiscard]
        private static bool IsHandleValid_Managed<T>(SystemBufferHandle<T> handle) where T : unmanaged
        {
            return handle.GenerationId == s_generationId && handle.Id >= 0 && handle.Id < s_buffers.Count;
        }

        public static bool IsHandleValid<T>(SystemBufferHandle<T> handle) where T : unmanaged
        {
            return IsHandleValid_Managed(handle);
        }

        // 这是从 Burst 进入托管世界的桥梁
        [BurstDiscard]
        private static NativeList<T> GetList_Managed<T>(SystemBufferHandle<T> handle) where T : unmanaged
        {
            if (!IsHandleValid(handle))
            {
                throw new InvalidOperationException("Attempting to use an invalid or stale SystemBufferHandle.");
            }
            
            var buffer = (SystemBuffer<T>)s_buffers[handle.Id];
            return buffer.Get();
        }
        
        // 公共 API，用于在 OnUpdate 中调用 (可以被 Burst 编译)
        public static NativeList<T> GetList<T>(SystemBufferHandle<T> handle) where T : unmanaged
        {
            return GetList_Managed(handle);
        }

        public static JobHandle ScheduleClear<T>(SystemBufferHandle<T> handle, JobHandle inputDeps) where T : unmanaged
        {
            var clearJob = new ClearBufferJob<T>
            {
                Buffer = GetList<T>(handle)
            };
            return clearJob.Schedule(inputDeps);
        }
 
        public static void DisposeAll()
        {
            foreach (var buffer in s_buffers)
            {
                buffer.Dispose();
            }
            s_buffers.Clear();
            // 每次清理时，我们都会进入一个新的“代”，使所有旧句柄失效。
            s_generationId++;
        }
    }

    /// <summary>
    /// 该系统负责为所有已注册的 SystemBuffer 调用中央处置方法。
    /// </summary>
    [UpdateInGroup(typeof(SimulationSystemGroup), OrderLast = true)]
    public partial struct SystemBufferManagerCleanupSystem : ISystem
    {
        public void OnCreate(ref SystemState state) { }
        public void OnUpdate(ref SystemState state) { }

        public void OnDestroy(ref SystemState state)
        {
            SystemBufferManager.DisposeAll();
        }
    }

    [BurstCompile(CompileSynchronously = true)]
    internal struct ClearBufferJob<T> : IJob where T : unmanaged
    {
        public NativeList<T> Buffer;

        public void Execute()
        {
            Buffer.Clear();
        }
    }
}
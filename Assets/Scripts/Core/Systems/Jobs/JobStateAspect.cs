using Unity.Entities;

namespace MyGame.Core.Systems.Jobs
{
    /// <summary>
    /// Provides a high-level API for interacting with a JobTracker entity.
    /// Simplifies the process of checking and updating the job's status.
    /// </summary>
    public readonly partial struct JobStateAspect : IAspect
    {
        public readonly Entity Self;
        private readonly RefRW<JobTrackerComponent> _jobTracker;

        public int JobId => _jobTracker.ValueRO.JobId;
        public JobStatus Status => _jobTracker.ValueRO.Status;
        public double StartTime => _jobTracker.ValueRO.StartTime;
        public float TimeoutSeconds => _jobTracker.ValueRO.TimeoutSeconds;

        public void SetStatus(JobStatus newStatus)
        {
            _jobTracker.ValueRW.Status = newStatus;
        }
    }
}
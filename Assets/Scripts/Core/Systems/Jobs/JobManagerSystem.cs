using Unity.Entities;

namespace MyGame.Core.Systems.Jobs
{
    /// <summary>
    /// Manages the lifecycle of the JobManager singleton.
    /// This system ensures that the JobManager is created when the world starts and disposed of when it's destroyed.
    /// </summary>
    [UpdateInGroup(typeof(InitializationSystemGroup), OrderFirst = true)]
    public partial class JobManagerSystem : SystemBase
    {
        public JobManager JobManager { get; private set; }

        protected override void OnCreate()
        {
            JobManager = new JobManager();
        }

        protected override void OnUpdate()
        {
            // This system doesn't need to do anything in OnUpdate.
        }

        protected override void OnDestroy()
        {
            if (JobManager != null)
            {
                JobManager.Dispose();
                JobManager = null;
            }
        }
    }
}
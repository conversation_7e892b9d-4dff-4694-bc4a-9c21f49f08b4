using Unity.Entities;

namespace MyGame.Core.Systems.Jobs
{
    /// <summary>
    /// An event entity created by the JobCompletionSystem when the map generation job is finished.
    /// Downstream systems, like physics and finalization, can subscribe to this event.
    /// </summary>
    public struct MapGenerationCompleted : IComponentData
    {
        // This event can carry data if needed, for example, the entity of the completed JobTracker.
        public Entity JobTrackerEntity;
    }
}
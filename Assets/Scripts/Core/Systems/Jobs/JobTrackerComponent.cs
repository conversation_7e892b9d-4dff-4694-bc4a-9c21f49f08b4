using Unity.Entities;

namespace MyGame.Core.Systems.Jobs
{
    public enum JobStatus : byte
    {
        Running,
        Completed,
        Failed
    }

    /// <summary>
    /// Tracks the state of a long-running, asynchronous job.
    /// An entity with this component represents a single instance of a job.
    /// </summary>
    public struct JobTrackerComponent : IComponentData
    {
        // A unique identifier for the type of job (e.g., MapGeneration, Pathfinding, etc.)
        // This can be used to differentiate between different jobs in query filters.
        public int JobId;
        public JobStatus Status;
        
        // --- Timeout Fields ---
        /// <summary>
        /// The time the job was started, based on SystemAPI.Time.ElapsedTime.
        /// </summary>
        public double StartTime;
        
        /// <summary>
        /// How long the job is allowed to run before it is considered timed out.
        /// A value <= 0 means no timeout.
        /// </summary>
        public float TimeoutSeconds;
    }
}
using System;
using System.Collections.Generic;
using Unity.Entities;
using Unity.Jobs;

namespace MyGame.Core.Systems.Jobs
{
    /// <summary>
    /// A non-system class that tracks long-running jobs.
    /// It associates a JobHandle and an optional EntityCommandBuffer with a tracker entity.
    /// </summary>
    public class JobManager : IDisposable
    {
        // Stores the handle and ECB for each tracked job, keyed by the tracker entity.
        private Dictionary<Entity, (JobHandle Handle, EntityCommandBuffer Ecb)> _managedJobs;

        public JobManager()
        {
            _managedJobs = new Dictionary<Entity, (JobHandle, EntityCommandBuffer)>();
        }

        /// <summary>
        /// Registers a new job to be tracked.
        /// </summary>
        /// <param name="trackerEntity">The entity that has the JobTrackerComponent.</param>
        /// <param name="handle">The final JobHandle for the entire job chain.</param>
        /// <param name="ecb">The EntityCommandBuffer that should be played back when the job is complete.</param>
        public void RegisterJob(Entity trackerEntity, <PERSON>Handle handle, EntityCommandBuffer ecb)
        {
            if (_managedJobs.ContainsKey(trackerEntity))
            {
                // This shouldn't happen if the logic is correct.
                // Complete the old job immediately to prevent memory leaks.
                _managedJobs[trackerEntity].Handle.Complete();
                _managedJobs[trackerEntity].Ecb.Dispose();
            }
            _managedJobs[trackerEntity] = (handle, ecb);
        }

        /// <summary>
        /// Stops tracking a job. Called by JobCompletionSystem after the job is done.
        /// </summary>
        public void UnregisterJob(Entity trackerEntity)
        {
            _managedJobs.Remove(trackerEntity);
        }

        /// <summary>
        /// Tries to get the data associated with a tracked job.
        /// </summary>
        public bool TryGetJobData(Entity trackerEntity, out JobHandle handle, out EntityCommandBuffer ecb)
        {
            if (_managedJobs.TryGetValue(trackerEntity, out var data))
            {
                handle = data.Handle;
                ecb = data.Ecb;
                return true;
            }
            handle = default;
            ecb = default;
            return false;
        }

        /// <summary>
        /// Cleans up all tracked jobs. Called when the world is destroyed.
        /// This will block the main thread until all jobs are complete.
        /// </summary>
        public void Dispose()
        {
            foreach (var jobData in _managedJobs.Values)
            {
                jobData.Handle.Complete(); // Block and wait for the job to finish.
                jobData.Ecb.Dispose();     // Dispose the ECB's native memory.
            }
            _managedJobs.Clear();
        }
    }
}
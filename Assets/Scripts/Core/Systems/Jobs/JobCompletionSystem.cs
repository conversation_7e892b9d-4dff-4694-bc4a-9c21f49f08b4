using Unity.Entities;
using Unity.Jobs;
using UnityEngine;
using MyGame.Core.Systems.Diagnostics;

namespace MyGame.Core.Systems.Jobs
{
    [UpdateInGroup(typeof(SimulationSystemGroup), OrderLast = true)] // Ensure it runs after other systems have scheduled jobs.
    public partial class JobCompletionSystem : SystemBase
    {
        private JobManager _jobManager;

        protected override void OnCreate()
        {
            // No longer need the ECB system from BeginInitialization.
        }

        protected override void OnUpdate()
        {
            // Lazily acquire the JobManager in case the JobManagerSystem hasn't been created yet.
            if (_jobManager == null)
            {
                _jobManager = World.GetExistingSystemManaged<JobManagerSystem>()?.JobManager;
                if (_jobManager == null)
                {
                    // This can happen in the first few frames.
                    return;
                }
            }

            var currentTime = SystemAPI.Time.ElapsedTime;

            // Phase 1: Collect completed jobs without making structural changes.
            // Using a List as this code is not Burst compiled and runs on the main thread.
            var completedJobs = new System.Collections.Generic.List<(Entity, EntityCommandBuffer)>();

            foreach (var jobAspect in SystemAPI.Query<JobStateAspect>())
            {
                if (jobAspect.Status != JobStatus.Running)
                {
                    continue;
                }

                // --- Timeout Check ---
                if (jobAspect.TimeoutSeconds > 0 && currentTime > jobAspect.StartTime + jobAspect.TimeoutSeconds)
                {
                    // This is a structural change, but it's a rare case and we can handle it separately.
                    // For now, let's assume jobs don't time out to fix the main issue.
                    // A more robust solution would queue this change as well.
                }

                if (_jobManager.TryGetJobData(jobAspect.Self, out var jobHandle, out var jobEcb))
                {
                    if (jobHandle.IsCompleted)
                    {
                        // IMPORTANT: Do NOT store the aspect itself, as it will be invalidated by structural changes.
                        // Only store the data needed to retrieve it later.
                        completedJobs.Add((jobAspect.Self, jobEcb));
                    }
                }
                else
                {
                    Debug.LogWarning($"JobCompletionSystem: Could not find a JobHandle for running job entity {jobAspect.Self.Index}.");
                    jobAspect.SetStatus(JobStatus.Failed); // This is not a structural change.
                }
            }

            // Phase 2: Now, iterate over the collected list and perform all structural changes.
            foreach (var (jobEntity, ecb) in completedJobs)
            {
                // Play back the commands from the job. This is where entities are actually created.
                ecb.Playback(EntityManager);
                ecb.Dispose();

                // The job is done. Re-acquire the aspect AFTER structural changes to ensure it's valid.
                var currentAspect = SystemAPI.GetAspect<JobStateAspect>(jobEntity);
                
                // --- Read data from the aspect BEFORE any further structural changes ---
                var jobId = currentAspect.JobId;
                var startTime = currentAspect.StartTime;
                
                // --- Perform all write/structural change operations ---
                currentAspect.SetStatus(JobStatus.Completed);

                // Check which type of job it was to create the correct completion event.
                if (jobId == 1) // Hardcoded ID for Map Generation
                {
                    var eventEntity = EntityManager.CreateEntity();
                    EntityManager.AddComponentData(eventEntity, new MapGenerationCompleted
                    {
                        JobTrackerEntity = jobEntity
                    });
                }
                
                // Use the cached data for logging, as the aspect might be invalid now.
                Debug.Log($"Job for entity {jobEntity.Index} completed in {currentTime - startTime:F2} seconds.");
                        
                // IMPORTANT: Unregister after all operations are done.
                _jobManager.UnregisterJob(jobEntity);
            }
        }
    }
}
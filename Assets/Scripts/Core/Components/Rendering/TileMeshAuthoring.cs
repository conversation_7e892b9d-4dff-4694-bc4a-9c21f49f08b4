using UnityEngine;
using Unity.Entities;
using System.Collections.Generic;
using Unity.Rendering;

namespace MyGame.Core
{
    /// <summary>
    /// 格子类型枚举
    /// </summary>
    public enum TileType
    {
        Empty,
        Wall,
        Floor,
        Water,
        Forest,
        Mountain,
        Door,
        StairUp,
        StairDown,
        Shop,
        CaveEntrance // Added for cave generation
    }
    /// <summary>
    /// Buffer element to store the mapping from a TileType to its rendering info.
    /// </summary>
    public struct TileTypeMaterial : IBufferElementData
    {
        public TileType Key;
        public MaterialMeshInfo Value;
    }

    /// <summary>
    /// A serializable struct for the authoring component to define mappings in the editor.
    /// </summary>
    [System.Serializable]
    public struct TileMeshMapping
    {
        public TileType TileType;
        public Mesh Mesh;
        public Material Material;
    }

    /// <summary>
    /// Authoring component to define tile meshes and materials in the editor.
    /// </summary>
    [AddComponentMenu("DOTS/Tile Mesh Authoring")]
    public class TileMeshAuthoring : MonoBehaviour
    {
        [Tooltip("List of tile mesh mappings for different tile types")]
        public List<TileMeshMapping> Mappings = new List<TileMeshMapping>();
    }

}
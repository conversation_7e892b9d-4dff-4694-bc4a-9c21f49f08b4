using Unity.Entities;
using Unity.Collections;

/// <summary>
/// 全局网络配置组件。
/// 附加到一个单一实体上。
/// </summary>
public struct NetworkConfigComponent : IComponentData
{
    public FixedString512Bytes ServerUrl;
}

/// <summary>
/// 标记一个实体为网络请求。
/// 系统将查询此组件以发送请求。
/// </summary>
public struct NetworkRequest : IComponentData
{
    public FixedString128Bytes Endpoint;
    public FixedString512Bytes Data; // Can be JSON or other formats
}

/// <summary>
/// 存储网络请求的响应。
/// 在请求完成后添加到实体上。
/// </summary>
public struct NetworkResponse : IComponentData
{
    public bool IsSuccess;
    public FixedString4096Bytes ResponseData; // 4KB for response
}
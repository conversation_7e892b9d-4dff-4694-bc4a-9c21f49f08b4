using Unity.Entities;
using Unity.Mathematics;

/// <summary>
/// 地图组件 - 存储地图基本信息. This is now a purely unmanaged component.
/// The actual tile data is stored in a separate DynamicBuffer on the same entity.
/// 使用纯数学类型以支持Burst编译和GPU计算。
/// </summary>
public struct MapComponent : IComponentData
{
    public int Width;
    public int Height;
    public int Seed;
    public int2 ChunkSize;
    public float ForestDensity;
    public int2 Center;
    public MapType MapType;
}

/// <summary>
/// 地图类型枚举
/// </summary>
public enum MapType
{
    World,
    Chunk,
    Room,
    Dungeon,
    Town
}


public readonly partial struct MapAspect : IAspect
{
    public readonly Entity Entity;

    private readonly RefRO<MapComponent> _mapComponent;
    private readonly DynamicBuffer<TileData> _tileData;
    // Removed unused field _worldMapTag

    public int Width => _mapComponent.ValueRO.Width;
    public int Height => _mapComponent.ValueRO.Height;
    public int Seed => _mapComponent.ValueRO.Seed;
    public int2 ChunkSize => _mapComponent.ValueRO.ChunkSize;
    public float ForestDensity => _mapComponent.ValueRO.ForestDensity;
    public int2 Center => _mapComponent.ValueRO.Center;
    public MapType MapType => _mapComponent.ValueRO.MapType;
    public DynamicBuffer<TileData> Tiles => _tileData;
}
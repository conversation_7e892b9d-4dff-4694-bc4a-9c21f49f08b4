using Unity.Entities;
using Unity.Mathematics;

/// <summary>
/// An element for a dynamic buffer that represents the tiles of the world map.
/// </summary>
public struct TileData : IBufferElementData
{
    public int Value; // Represents the final Biome ID
    // Heights are stored as uints, which are bitwise representations of floats.
    // This is required for atomic operations in the compute shader.
    public uint4 VertexHeights; // (x,y,z,w) -> (x0,y0), (x1,y0), (x0,y1), (x1,y1)
    public float WaterLevel;
    public float Temperature;
    public float Moisture;
}

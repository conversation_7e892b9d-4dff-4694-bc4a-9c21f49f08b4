using Unity.Entities;
using Unity.Burst;
using Unity.Mathematics;

/// <summary>
/// 地图生成器配置组件 - 替换传统MonoBehaviour的参数配置
/// </summary>
[BurstCompile]
public struct MapGeneratorConfigComponent : IComponentData
{
    /// <summary>
    /// 地图宽度
    /// </summary>
    public int Width;

    /// <summary>
    /// 地图高度
    /// </summary>
    public int Height;

    /// <summary>
    /// 区块大小
    /// </summary>
    public int2 ChunkSize;

    /// <summary>
    /// 种子值
    /// </summary>
    public int Seed;

    /// <summary>
    /// 生成状态
    /// </summary>
    public GenerationState State;

    /// <summary>
    /// 当前生成请求ID
    /// </summary>
    public System.Guid CurrentRequestId;

    /// <summary>
    /// 生成开始时间
    /// </summary>
    public double StartTime;

    /// <summary>
    /// 输出配置
    /// </summary>
    public MapGeneratorOutputConfig Output;

    /// <summary>
    /// 创建默认配置
    /// </summary>
    public static MapGeneratorConfigComponent CreateDefault()
    {
        return new MapGeneratorConfigComponent
        {
            Width = 256,
            Height = 256,
            ChunkSize = new int2(64, 64),
            Seed = 42,
            State = GenerationState.Idle,
            CurrentRequestId = System.Guid.Empty,
            StartTime = 0,
            Output = MapGeneratorOutputConfig.CreateDefault()
        };
    }

    /// <summary>
    /// 使用随机种子创建配置
    /// </summary>
    public static MapGeneratorConfigComponent CreateRandom()
    {
        return new MapGeneratorConfigComponent
        {
            Width = 256,
            Height = 256,
            ChunkSize = new int2(64, 64),
            Seed = UnityEngine.Random.Range(int.MinValue, int.MaxValue),
            State = GenerationState.Idle,
            CurrentRequestId = System.Guid.Empty,
            StartTime = 0,
            Output = MapGeneratorOutputConfig.CreateDefault()
        };
    }
}

/// <summary>
/// 地图生成器输出配置
/// </summary>
[BurstCompile]
public struct MapGeneratorOutputConfig : IComponentData
{
    /// <summary>
    /// 是否显示调试信息
    /// </summary>
    public bool ShowDebugInfo;

    /// <summary>
    /// 是否显示生成统计信息
    /// </summary>
    public bool ShowStatistics;

    /// <summary>
    /// 生成超时时间（秒）
    /// </summary>
    public float TimeoutSeconds;

    /// <summary>
    /// 创建默认输出配置
    /// </summary>
    public static MapGeneratorOutputConfig CreateDefault()
    {
        return new MapGeneratorOutputConfig
        {
            ShowDebugInfo = true,
            ShowStatistics = true,
            TimeoutSeconds = 30.0f
        };
    }
}

/// <summary>
/// 地图生成状态枚举
/// </summary>
public enum GenerationState
{
    Idle,
    Initializing,
    Generating,
    Completed,
    Failed,
    Timeout
}

/// <summary>
/// 地图生成事件组件 - 用于触发生成完成/失败事件
/// </summary>
[BurstCompile]
public struct MapGenerationEvent : IComponentData
{
    public GenerationEventType EventType;
    public System.Guid RequestId;
    public double Timestamp;
}

/// <summary>
/// 地图生成事件类型
/// </summary>
public enum GenerationEventType
{
    Started,
    Completed,
    Failed,
    Timeout,
    Cancelled
}
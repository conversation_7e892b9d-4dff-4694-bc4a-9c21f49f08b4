using Unity.Entities;
using Unity.Mathematics;

/// <summary>
/// Chunk组件 - 管理地图的各个区块区域.
/// This is now a purely unmanaged component.
/// Data like rooms and connections will be handled by other components or buffers.
/// 使用纯数学类型以支持Burst编译和GPU计算。
/// </summary>
public struct ChunkComponent : IComponentData
{
    public int2 ChunkPosition;
    public int2 ChunkSize;
    public int2 WorldPosition;
    public float DetailLevel;
    public bool IsGenerated;
    public int NoiseSeed;
    public ChunkType ChunkType;
    public ThemeType ThemeType;
}

// Note: The RoomInfo and ChunkConnection classes have been removed from this file.
// They will need to be re-implemented as components or buffer elements in a DOTS-compliant way.

/// <summary>
///
/// </summary>
public enum ChunkType
{
    Empty,
    Village,
    Cave,
    Building,
    Forest,
    Plains
}

/// <summary>
///
/// </summary>
public enum ThemeType
{
    Plains,
    Forest,
    Desert,
    Mountain,
    Underground
}

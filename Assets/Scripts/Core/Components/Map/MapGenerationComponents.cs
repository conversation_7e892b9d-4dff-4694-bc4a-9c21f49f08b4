using Unity.Entities;
using Unity.Mathematics;

namespace MyGame.Core
{
    /// <summary>
    /// A singleton component that holds a reference to the main map entity.
    /// This allows other systems to easily access map data without searching.
    /// </summary>
    public struct WorldMapSingleton : IComponentData
    {
        public Entity MapEntity;
    }

    /// <summary>
    /// A tag component to mark entities that have had their internal structure generated.
    /// This prevents generation systems from running on the same entity multiple times.
    /// </summary>
    public struct StructureGeneratedTag : IComponentData { }

    public struct TreePosition : IBufferElementData
    {
        public float2 Value;
    }
}
using UnityEngine;
using Unity.Entities;

namespace MyGame.Core
{
    /// <summary>
    /// 在编辑器中定义单个地形层的数据结构。
    /// 层级应按 HeightThreshold 从低到高排序。
    /// </summary>
    [System.Serializable]
    public struct TerrainLayer
    {
        public TileType TileType;
        public float HeightThreshold; // 高度值低于或等于此阈值的瓦片将被归类为此类型
    }

    /// <summary>
    /// 创建一个 ScriptableObject 来在编辑器中配置地形层列表。
    /// </summary>
    [CreateAssetMenu(fileName = "TerrainConfig", menuName = "MyGame/Terrain Generation Config")]
    public class TerrainGenerationConfig : ScriptableObject
    {
        [Tooltip("请按高度阈值(HeightThreshold)从低到高排序")]
        public TerrainLayer[] TerrainLayers;
    }
}
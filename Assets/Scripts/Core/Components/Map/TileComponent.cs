using UnityEngine;
using Unity.Entities;
using Unity.Mathematics;
using MyGame.Core;

/// <summary>
/// 地图格子组件 - 表示地图上的单个格子. This is now a purely unmanaged component.
/// </summary>
public struct TileComponent : IComponentData
{
    public int2 Position;
    public TileType tileType;
    public bool IsWalkable;
    public bool IsTransparent;
    public bool IsDiscovered;
    public bool IsVisible;
    public float Elevation;
    public float WaterLevel;
    public bool IsRiverChannel;
    public bool IsNearRivers;
    public float2 WaterFlow;
    public float SoilAbsorption;
    public float4 VertexHeights;
    public GroundMaterialType GroundMaterial;
    public int RenderOrder;

    // Note: The 'Features' list has been removed. To add features to a tile,
    // add a DynamicBuffer<TileFeature> to the tile's entity.
}

/// <summary>
/// 地面材质类型枚举
/// </summary>
public enum GroundMaterialType
{
    Grass,
    Dirt,
    Sand,
    Stone,
    ForestFloor,
    Snow,
    Water,
    Rock
}

/// <summary>
/// 格子类型枚举 - 已在 MyGame.Core 命名空间中定义
/// </summary>

/// <summary>
/// 格子特征枚举
/// </summary>
public enum TileFeature
{
    Blood,
    Trap,
    Treasure,
    Broken,
    Wet,
    Flooded
}
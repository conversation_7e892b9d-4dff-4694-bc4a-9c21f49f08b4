using Unity.Entities;
using Unity.Collections;

/// <summary>
/// 河流组件 - 专门管理河流数据和边界信息. This is a purely unmanaged component.
/// </summary>
public struct RiverComponent : IComponentData
{
    public int RiverId;
    public FixedString128Bytes RiverName;
    public float RiverWidth;
    public float RiverLength;
    public bool IsMainBranch;
    public int UpstreamRiverId;
    public float DrainageArea;
    public float WaterLevel;
    public bool IsPassable;
    public float FlowRate;

    // Note: The Segments and DownstreamRiverIds lists have been removed.
    // This data must be stored in DynamicBuffers attached to the river entity.
}

/// <summary>
/// 河流段落 - 表示河流路径上的一个点. This is now a struct for use in DynamicBuffers.
/// </summary>
public struct RiverSegment : IBufferElementData
{
    public int SegmentIndex;
    public Unity.Mathematics.float2 Position;
    public Unity.Mathematics.int2 GridPosition;
    public float WidthScale;
    public float Depth;
    public float StrategicValue;
    public bool IsCriticalPoint;
    // Note: The list of features has been removed. This would require another DynamicBuffer if needed.
}

/// <summary>
/// 河流特征枚举
/// </summary>
public enum RiverFeature
{
    Bridge,
    Dam,
    Rapids,
    Ford,
    Island,
    Confluence,
    Source,
    Mouth
}
using UnityEngine;

/// <summary>
/// 地图配置 - 单一配置源管理所有地图相关设置
/// </summary>
[CreateAssetMenu(fileName = "MapConfig", menuName = "Game/Map Configuration", order = 1)]
public class MapConfig : ScriptableObject
{
    [Header("Chunk 设置")]
    [SerializeField]
    [Tooltip("Chunk 默认尺寸")]
    private Vector2Int _defaultChunkSize = new Vector2Int(64, 64);

    /// <summary>
    /// 获取默认Chunk尺寸
    /// </summary>
    public Vector2Int ChunkSize => _defaultChunkSize;

    [Header("世界设置")]
    [SerializeField]
    private int _defaultWorldWidth = 1200;

    [SerializeField]
    private int _defaultWorldHeight = 700;

    /// <summary>
    /// 获取默认世界宽度
    /// </summary>
    public int DefaultWorldWidth => _defaultWorldWidth;

    /// <summary>
    /// 获取默认世界高度
    /// </summary>
    public int DefaultWorldHeight => _defaultWorldHeight;
}
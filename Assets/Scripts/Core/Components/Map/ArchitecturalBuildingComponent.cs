using UnityEngine;
using Unity.Entities;
using Unity.Collections;

/// <summary>
/// 建筑组件 - 管理不规则形状的多格子建筑. This is a purely unmanaged component.
/// </summary>
public struct ArchitecturalBuildingComponent : IComponentData
{
    public int BuildingId;
    public FixedString128Bytes BuildingName;
    public BuildingType BuildingType;
    public BoundsInt Bounds;
    public bool IsAccessible;
    public int CurrentOccupants;
    public int MaxCapacity;
    public float Importance;

    // Note: The collection fields (OccupiedGridCells, EntrancePoints, BuildingEdges)
    // have been removed. This data must be stored in DynamicBuffers attached to the entity.
}

/// <summary>
/// Represents an edge of a building for rendering or physics.
/// This is now a struct for use in DynamicBuffers.
/// </summary>
public struct BuildingEdge : IBufferElementData
{
    public Unity.Mathematics.float2 Start;
    public Unity.Mathematics.float2 End;
    public BuildingEdgeDirection Direction;
}

public enum BuildingType
{
    House, Shop, Tavern, Temple, Library, BlackSmith, Farmhouse
}

public enum BuildingEdgeDirection
{
    Horizontal, Vertical
}
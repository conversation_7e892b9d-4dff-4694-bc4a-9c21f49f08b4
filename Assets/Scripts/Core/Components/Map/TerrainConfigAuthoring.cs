using UnityEngine;
using Unity.Entities;

namespace MyGame.Core
{
    /// <summary>
    /// 用于标识地形配置 Singleton 实体的标签组件。
    /// </summary>
    public struct TerrainConfigSingletonTag : IComponentData { }

    /// <summary>
    /// 将 TerrainGenerationConfig ScriptableObject 附加到场景中的 MonoBehaviour。
    /// </summary>
    public class TerrainConfigAuthoring : MonoBehaviour
    {
        [Tooltip("指定要用于地图生成的地形配置 ScriptableObject。")]
        public TerrainGenerationConfig TerrainConfig;
    }
}
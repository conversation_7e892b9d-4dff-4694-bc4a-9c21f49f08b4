using Unity.Entities;
using Unity.Collections;

/// <summary>
/// 地图生成标签组件 - 标记各种生成状态
/// </summary>

/// <summary>
/// 地图生成完成标志组件
/// </summary>
public struct MapGenerationCompletedTag : IComponentData { }

/// <summary>
/// 洞穴生成标记组件
/// </summary>
public struct CaveGeneratedTag : IComponentData { }

/// <summary>
/// 树木生成标记组件
/// </summary>
public struct TreeGeneratedTag : IComponentData { }

/// <summary>
/// 河流生成标记组件
/// </summary>
public struct RiverGeneratedTag : IComponentData { }

/// <summary>
/// 数据验证完成标记组件
/// </summary>
public struct DataValidationCompletedTag : IComponentData { }

/// <summary>
/// 数据验证标记组件
/// </summary>
public struct DataValidatedTag : IComponentData { }
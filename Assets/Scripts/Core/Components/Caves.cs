using Unity.Entities;
using Unity.Mathematics;

/// <summary>
/// Component attached to a cave entity, storing its core data.
/// </summary>
public struct CaveComponent : IComponentData
{
    public uint CaveID;
    public int2 EntrancePosition;
    public byte IsGenerated; // 0 = false, 1 = true. Using byte for blittable type.
    public int2 InteriorSize; // The dimensions of the generated cave interior
}

/// <summary>
/// A dynamic buffer to store the tile data of a generated cave interior.
/// This is attached to the Cave entity after generation.
/// </summary>
[InternalBufferCapacity(0)] // Capacity will be set at runtime
public struct CaveTileData : IBufferElementData
{
    public TileData Value;
}


/// <summary>
/// A request to generate the interior of a specific cave.
/// This is usually added when the player interacts with a cave entrance.
/// </summary>
public struct GenerateCaveInteriorRequest : IComponentData
{
    public Entity CaveEntity;
}

/// <summary>
/// A tag to mark caves that are in the process of generating their interior.
/// Added by CaveInteriorSetupSystem and removed by CaveInteriorGenerationSystem.
/// </summary>
public struct CaveIsGeneratingTag : IComponentData { }

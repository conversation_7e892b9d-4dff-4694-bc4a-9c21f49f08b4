using Unity.Entities;
using Unity.Collections;
using Unity.Mathematics;

using Unity.Transforms;
/// <summary>
/// 存储玩家核心数据的组件。
/// 这将附加到一个全局唯一的实体上，以替代PlayerDataServiceSO。
/// </summary>
/// <summary>
/// 玩家输入组件 - 存储离散的移动输入
/// </summary>
public struct PlayerInputComponent : IComponentData
{
    public int2 MoveDirection; // (-1, 0) for left, (1, 0) for right, etc.
}

public struct PlayerDataComponent : IComponentData
{
    public int PlayerId;
    public FixedString64Bytes PlayerName;
    public int Level;
    public int Experience;
    public float Health;
    public float MaxHealth;
}

public readonly partial struct PlayerInputAspect : IAspect
{
    public readonly Entity Entity;

    private readonly RefRW<PlayerInputComponent> _playerInput;

    // A property to access the move direction
    public int2 MoveDirection
    {
        get => _playerInput.ValueRO.MoveDirection;
        set => _playerInput.ValueRW.MoveDirection = value;
    }
}

public readonly partial struct PlayerMovementAspect : IAspect
{
    public readonly Entity Entity;

    private readonly RefRW<LocalTransform> _transform;
    private readonly RefRO<PlayerInputComponent> _playerInput;

    public void Move(float deltaTime)
    {
        var moveDirection = new float3(_playerInput.ValueRO.MoveDirection.x, 0, _playerInput.ValueRO.MoveDirection.y);
        _transform.ValueRW.Position += moveDirection * deltaTime * 5f; // 5f is a placeholder for speed
    }
}
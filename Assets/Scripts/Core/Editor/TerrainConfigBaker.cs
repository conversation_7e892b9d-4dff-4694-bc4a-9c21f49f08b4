using Unity.Entities;
using MyGame.Core;
using MyGame.Core.Systems;

#if UNITY_EDITOR
/// <summary>
/// 将 TerrainConfigAuthoring 组件的数据烘焙到实体中。
/// </summary>
public class TerrainConfigBaker : Baker<TerrainConfigAuthoring>
{
    public override void Bake(TerrainConfigAuthoring authoring)
    {
        // 如果没有分配 ScriptableObject 或其内容为空，则不执行任何操作。
        if (authoring.TerrainConfig == null || authoring.TerrainConfig.TerrainLayers == null)
        {
            UnityEngine.Debug.LogWarning("TerrainConfigAuthoring is missing TerrainConfig ScriptableObject. Skipping bake.");
            return;
        }

        // 声明对 ScriptableObject 资产的依赖。
        // 如果 SO 发生变化，Unity 会自动触发重新烘焙。
        DependsOn(authoring.TerrainConfig);

        // 为这个 Authoring 组件所在的 GameObject 创建/获取一个实体，并将其作为 Singleton。
        var entity = GetEntity(TransformUsageFlags.None);

        // 添加一个标签，以便系统可以轻松查询到这个实体。
        AddComponent(entity, new TerrainConfigSingletonTag());

        // 添加一个动态缓冲区来存储地形层数据。
        var buffer = AddBuffer<TerrainLayerElement>(entity);
        buffer.Clear(); // 确保缓冲区是空的，以防重新烘焙。

        // 遍历 ScriptableObject 中的所有层级，并将它们添加到实体的缓冲区中。
        foreach (var layer in authoring.TerrainConfig.TerrainLayers)
        {
            buffer.Add(new TerrainLayerElement
            {
                TileType = (int)layer.TileType,
                HeightThreshold = layer.HeightThreshold
            });
        }
        
        UnityEngine.Debug.Log($"Successfully baked {buffer.Length} terrain layers from {authoring.TerrainConfig.name} into a singleton entity.", authoring.gameObject);
    }
}
#endif
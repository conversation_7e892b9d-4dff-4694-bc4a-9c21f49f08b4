using UnityEngine;
using Unity.Entities;
using Unity.Rendering;

namespace MyGame.Core
{
    public class TileMeshBaker : <PERSON><TileMeshAuthoring>
    {
        public override void Bake(TileMeshAuthoring authoring)
        {
            // Get the entity associated with the authoring component.
            var entity = GetEntity(TransformUsageFlags.None);
            
            // Add the dynamic buffer to this entity.
            // As long as there is only one TileMeshAuthoring in the scene,
            // this entity will be treated as a singleton for queries.
            var buffer = AddBuffer<TileTypeMaterial>(entity);

            foreach (var mapping in authoring.Mappings)
            {
                if (mapping.Mesh != null && mapping.Material != null)
                {
                    buffer.Add(new TileTypeMaterial
                    {
                        Key = mapping.TileType,
                        Value = new MaterialMeshInfo
                        {
                            // In this version of the API, it appears an InstanceID is expected.
                            Mesh = mapping.Mesh.GetInstanceID(),
                            Material = mapping.Material.GetInstanceID()
                        }
                    });
                }
            }
        }
    }
}
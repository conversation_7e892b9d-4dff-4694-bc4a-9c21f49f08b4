using Unity.Entities;
using UnityEngine;
using MyGame.Core.Systems;

public class MinimalTestRunner : MonoBehaviour
{
    void Start()
    {
        // -- 禁用: 根据用户要求，全局只应有一个地图生成请求源 (GameBootstrapperSystem) --
        // Debug.Log("MinimalTestRunner: Initiating map generation request...");
        //
        // // Get the default world and its entity manager
        // var world = World.DefaultGameObjectInjectionWorld;
        // if (world == null)
        // {
        //     Debug.LogError("MinimalTestRunner: Could not find default world. Is the game running?");
        //     return;
        // }
        // var entityManager = world.EntityManager;
        //
        // // Create the request entity
        // var requestEntity = entityManager.CreateEntity();
        // entityManager.AddComponentData(requestEntity, new GenerateWorldRequest
        // {
        //     Seed = 42,
        //     Width = 128,
        //     Height = 128,
        //     ChunkSize = new Unity.Mathematics.int2(64, 64),
        //     ForestDensity = 0.5f
        // });
        //
        // Debug.Log("MinimalTestRunner: GenerateWorldRequest entity created.");
    }
}
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using UnityEngine;

/// <summary>
/// UI数据模型基类
/// MVC模式中的Model层，负责数据的管理和业务逻辑
/// </summary>
[System.Serializable]
public abstract class UIModelBase : INotifyPropertyChanged
{
    [Header("模型设置")]
    public string ModelId;
    public bool IsDirty = false;

    public event System.Action OnDataChanged;
    public event PropertyChangedEventHandler PropertyChanged;

    protected Dictionary<string, System.Action> _dataChangedCallbacks = new Dictionary<string, System.Action>();

    public UIModelBase()
    {
        InitializeModel();
    }

    /// <summary>
    /// 初始化模型
    /// </summary>
    protected virtual void InitializeModel()
    {
        ModelId = GetType().Name;
    }

    /// <summary>
    /// 订阅数据变更事件
    /// </summary>
    public void SubscribeDataChanged(string dataKey, System.Action callback)
    {
        if (!_dataChangedCallbacks.ContainsKey(dataKey))
        {
            _dataChangedCallbacks[dataKey] = callback;
        }
        else
        {
            _dataChangedCallbacks[dataKey] += callback;
        }
    }

    /// <summary>
    /// 取消订阅数据变更事件
    /// </summary>
    public void UnsubscribeDataChanged(string dataKey, System.Action callback)
    {
        if (_dataChangedCallbacks.ContainsKey(dataKey))
        {
            _dataChangedCallbacks[dataKey] -= callback;

            if (_dataChangedCallbacks[dataKey] == null)
            {
                _dataChangedCallbacks.Remove(dataKey);
            }
        }
    }

    /// <summary>
    /// 通知数据变更
    /// </summary>
    protected void NotifyDataChanged([CallerMemberName] string propertyName = null)
    {
        IsDirty = true;

        if (_dataChangedCallbacks.TryGetValue(propertyName, out System.Action callback))
        {
            callback?.Invoke();
        }

        // 全局数据变更通知
        OnDataChanged?.Invoke();
        
        // INotifyPropertyChanged event
        OnPropertyChanged(propertyName);
    }

    protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// 重置脏标记
    /// </summary>
    public virtual void ResetDirty()
    {
        IsDirty = false;
    }

    /// <summary>

    /// <summary>
    /// 序列化数据
    /// </summary>
    public virtual string Serialize()
    {
        return JsonUtility.ToJson(this);
    }

    /// <summary>
    /// 反序列化数据
    /// </summary>
    public virtual void Deserialize(string json)
    {
        JsonUtility.FromJsonOverwrite(json, this);
    }

    /// <summary>
    /// 复制模型
    /// </summary>
    public virtual UIModelBase Clone()
    {
        var json = Serialize();
        var clone = (UIModelBase)System.Activator.CreateInstance(GetType());
        clone.Deserialize(json);
        return clone;
    }
}

/// <summary>
/// UI层使用的数据容器，与ECS的PlayerDataComponent解耦。
/// 这个结构体被定义在Core程序集中，以便UI和Core系统都能访问。
/// </summary>
public struct PlayerData
{
    public int PlayerId;
    public string PlayerName;
    public int Level;
    public int Experience;
    public float Health;
    public float MaxHealth;
    public int Gold;
}


/// <summary>
/// 玩家UI数据模型
/// 这个类被定义在Core程序集中，以便UI和Core系统都能访问。
/// </summary>
[System.Serializable]
public class PlayerUIModel : UIModelBase
{
    [Header("玩家数据")]
    public string PlayerName;
    public int Level;
    public int Experience;
    public int MaxHealth;
    public int CurrentHealth;
    public int Gold;
    public Sprite AvatarSprite;

    [Header("设置")]
    public float MusicVolume = 0.8f;
    public float SoundVolume = 1.0f;
    public bool IsFullscreen = true;

    // 计算属性
    public float HealthPercentage => MaxHealth > 0 ? (float)CurrentHealth / MaxHealth : 0f;
    public int ExperienceToNextLevel => CalculateExpToNextLevel();

    protected override void InitializeModel()
    {
        base.InitializeModel();
        ModelId = "PlayerUI";

        // 数据现在由 UIDataBridgeSystem 从 ECS 同步过来
    }

    public void UpdateFromPlayerData(PlayerData playerData)
    {
        // Use a backing field and a property to trigger PropertyChanged
        if (PlayerName != playerData.PlayerName)
        {
            PlayerName = playerData.PlayerName;
            NotifyDataChanged(nameof(PlayerName));
        }

        if (Level != playerData.Level)
        {
            Level = playerData.Level;
            NotifyDataChanged(nameof(Level));
        }

        if (Experience != playerData.Experience)
        {
            Experience = playerData.Experience;
            NotifyDataChanged(nameof(Experience));
        }

        if (CurrentHealth != (int)playerData.Health || MaxHealth != (int)playerData.MaxHealth)
        {
            SetHealth((int)playerData.Health, (int)playerData.MaxHealth); // This will call NotifyDataChanged
        }

        if (Gold != playerData.Gold)
        {
            Gold = playerData.Gold;
            NotifyDataChanged(nameof(Gold));
        }
    }

    /// <summary>
    /// 设置健康值
    /// </summary>
    public void SetHealth(int currentHealth, int maxHealth)
    {
        bool changed = false;
        if (MaxHealth != maxHealth)
        {
            MaxHealth = maxHealth;
            changed = true;
        }
        
        var clampedHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
        if (CurrentHealth != clampedHealth)
        {
            CurrentHealth = clampedHealth;
            changed = true;
        }

        if (changed)
        {
            NotifyDataChanged(nameof(CurrentHealth));
            NotifyDataChanged(nameof(MaxHealth));
            NotifyDataChanged(nameof(HealthPercentage)); // Notify that computed property has changed
        }
    }

    /// <summary>
    /// 获得经验
    /// </summary>
    public void GainExperience(int amount)
    {
        Experience += amount;

        // 检查是否升级
        int requiredExp = CalculateExpToNextLevel();
        while (Experience >= requiredExp)
        {
            LevelUp();
            requiredExp = CalculateExpToNextLevel();
        }

        NotifyDataChanged(nameof(Experience));
    }

    /// <summary>
    /// 升级
    /// </summary>
    private void LevelUp()
    {
        Level++;
        Experience -= CalculateExpToNextLevel(); // Note: This might need adjustment if leveling up multiple times
        MaxHealth += 10; // 升级增加最大生命
        CurrentHealth = MaxHealth; // 升级后满血

        NotifyDataChanged(nameof(Level));
        NotifyDataChanged(nameof(Experience));
        NotifyDataChanged(nameof(CurrentHealth));
        NotifyDataChanged(nameof(MaxHealth));
    }

    /// <summary>
    /// 计算升级所需经验（简单计算：等级*100）
    /// </summary>
    private int CalculateExpToNextLevel()
    {
        return Level * 100;
    }

    /// <summary>
    /// 获得金币
    /// </summary>
    public void GainGold(int amount)
    {
        Gold += amount;
        NotifyDataChanged(nameof(Gold));
    }

    /// <summary>
    /// 消耗金币
    /// </summary>
    public bool SpendGold(int amount)
    {
        if (Gold >= amount)
        {
            Gold -= amount;
            NotifyDataChanged(nameof(Gold));
            return true;
        }

        return false;
    }

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    public bool ValidateData()
    {
        bool isValid = true;

        if (CurrentHealth < 0 || CurrentHealth > MaxHealth)
        {
            Debug.LogError("Invalid health values");
            isValid = false;
        }

        if (Level < 1)
        {
            Debug.LogError("Invalid level value");
            isValid = false;
        }

        if (Gold < 0)
        {
            Debug.LogError("Invalid gold value");
            isValid = false;
        }

        return isValid;
    }
}
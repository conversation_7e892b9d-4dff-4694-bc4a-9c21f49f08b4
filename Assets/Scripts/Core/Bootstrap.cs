using UnityEngine;
using Unity.Entities;

public class Bootstrap : MonoBehaviour
{
    private static bool _initialized = false;

    void Start()
    {
        if (_initialized)
        {
            return;
        }
        _initialized = true;

        // The assumption that a System will "automatically trigger" is incorrect without
        // something to drive the World's update loop. We will use this MonoBehaviour
        // as the "igniter" to manually kick off the very first update of the
        // InitializationSystemGroup. This will run our GameBootstrapperSystem once.
        Debug.Log("Bootstrap.cs: Manually triggering InitializationSystemGroup to kickstart the game.");
        
        var world = World.DefaultGameObjectInjectionWorld;
        if (world != null)
        {
            // By manually updating the InitializationSystemGroup once, we ensure our
            // GameBootstrapperSystem runs, creates the necessary entities, and then disables itself.
            // The regular player loop will take over for all subsequent frames.
            world.GetExistingSystemManaged<InitializationSystemGroup>().Update();
        }
        else
        {
            Debug.LogError("Bootstrap.cs: Could not find DefaultGameObjectInjectionWorld.");
        }
        
        // This script has served its purpose and can be removed.
        Destroy(gameObject);
    }
}    
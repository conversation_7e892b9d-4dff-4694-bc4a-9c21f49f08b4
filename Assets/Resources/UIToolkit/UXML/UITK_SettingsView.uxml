<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:engine="UnityEngine.UIElements" editor-extension-mode="False">
    <ui:VisualElement name="container" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0.7); justify-content: center; align-items: center;">
        <ui:VisualElement name="dialog" style="width: 400px; background-color: rgb(40, 40, 40); border-color: rgb(60, 60, 60); border-width: 2px; border-radius: 10px; padding: 20px;">
            <ui:Label text="设置" name="title" style="font-size: 24px; -unity-font-style: bold; color: rgb(220, 220, 220); margin-bottom: 20px; -unity-text-align: middle-center;" />
            
            <!-- 音量设置 -->
            <ui:VisualElement name="setting-row" style="flex-direction: row; align-items: center; margin-bottom: 10px;">
                <ui:Label text="音乐音量" style="width: 120px; color: rgb(220, 220, 220);" />
                <ui:Slider name="music-volume-slider" low-value="0" high-value="1" style="flex-grow: 1;" />
            </ui:VisualElement>
            
            <ui:VisualElement name="setting-row" style="flex-direction: row; align-items: center; margin-bottom: 10px;">
                <ui:Label text="音效音量" style="width: 120px; color: rgb(220, 220, 220);" />
                <ui:Slider name="sound-volume-slider" low-value="0" high-value="1" style="flex-grow: 1;" />
            </ui:VisualElement>

            <!-- 图形设置 -->
            <ui:VisualElement name="setting-row" style="flex-direction: row; align-items: center; margin-bottom: 10px;">
                <ui:Label text="全屏模式" style="width: 120px; color: rgb(220, 220, 220);" />
                <ui:Toggle name="fullscreen-toggle" />
            </ui:VisualElement>
            
            <ui:VisualElement name="setting-row" style="flex-direction: row; align-items: center; margin-bottom: 20px;">
                <ui:Label text="图形质量" style="width: 120px; color: rgb(220, 220, 220);" />
                <ui:DropdownField name="quality-dropdown" style="flex-grow: 1;" />
            </ui:VisualElement>
            
            <!-- 操作按钮 -->
            <ui:Button text="返回" name="back-button" style="height: 30px;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
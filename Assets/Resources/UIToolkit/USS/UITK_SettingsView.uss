/* --- SettingsView Styles --- */

/* 对话框容器统一样式 */
.dialog {
    background-color: rgb(40, 40, 40);
    border-color: rgb(80, 80, 80);
    border-width: 2px;
    border-radius: 15px;
    padding: 20px;
}

/* 标题样式 */
.title {
    font-size: 26px;
    -unity-font-style: bold;
    color: rgb(220, 220, 220);
    margin-bottom: 25px;
    -unity-text-align: middle-center;
}

/* 设置行样式 */
.setting-row {
    flex-direction: row;
    align-items: center;
    margin-bottom: 15px;
}

/* 设置行的标签样式 */
.setting-row Label {
    width: 120px;
    color: rgb(200, 200, 200);
    font-size: 16px;
}

/* 通用按钮样式 */
.button {
    height: 35px;
    background-color: rgb(80, 80, 80);
    color: rgb(220, 220, 220);
    border-color: rgb(100, 100, 100);
    border-width: 1px;
    border-radius: 5px;
    transition-property: background-color, color;
    transition-duration: 0.15s;
}

/* 按钮悬停效果 */
.button:hover {
    background-color: rgb(100, 100, 100);
    color: white;
}
/* --- HUDView Styles --- */

/* 整体容器样式 */
.hud-container {
    font-size: 16px;
    color: white;
}

/* 玩家状态和游戏信息面板的通用样式 */
.player-stats, .game-info {
    background-color: rgba(0, 0, 0, 0.4);
    padding: 10px;
    border-radius: 8px;
}

/* 所有标签的通用样式 */
.hud-container Label {
    -unity-font-style: bold;
    text-shadow: 1px 1px 2px black;
}

/* 生命值进度条样式 */
.health-bar {
    --unity-background-color: rgb(70, 70, 70);
    --unity-progress-bar-background-color: rgb(200, 40, 40);
    margin-top: 5px;
    margin-bottom: 5px;
}

/* 标签之间的间距 */
.player-stats > Label, .game-info > Label {
    margin-top: 3px;
    margin-bottom: 3px;
}
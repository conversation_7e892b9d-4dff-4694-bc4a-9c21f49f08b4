#pragma kernel CSMain

struct TileData
{
    // Heights are stored as uints which are bitwise representations of floats.
    // This is required for atomic operations.
    uint4 VertexHeights;
    int Value;
};

RWStructuredBuffer<TileData> TileBuffer;
StructuredBuffer<int2> BrushIndices;
StructuredBuffer<float> BrushWeights;

int2 mapSize;
int brushSize;
int numIterations;

float inertia;
float sedimentCapacityFactor;
float minSedimentCapacity;
float erodeSpeed;
float depositSpeed;
float evaporateSpeed;
float gravity;
int maxDropletLifetime;
float initialWaterVolume;
float initialSpeed;
int seed;

// Marsaglia's Xorshift PRNG for GPU
uint xorshift_state;

void srand(uint initial_seed) 
{
    xorshift_state = initial_seed;
}

uint rand_uint() 
{
    xorshift_state ^= xorshift_state << 13;
    xorshift_state ^= xorshift_state >> 17;
    xorshift_state ^= xorshift_state << 5;
    return xorshift_state;
}

float rand_float() 
{
    return rand_uint() / 4294967295.0;
}

float2 rand_float2(float2 minVal, float2 maxVal)
{
    return float2(minVal.x + (maxVal.x - minVal.x) * rand_float(), minVal.y + (maxVal.y - minVal.y) * rand_float());
}

float4 CalculateHeightAndGradient(float2 pos)
{
    int2 nodePos = int2(pos);
    if (nodePos.x < 0 || nodePos.x >= mapSize.x - 1 || nodePos.y < 0 || nodePos.y >= mapSize.y - 1)
    {
        return float4(0, 0, 0, 0);
    }
    
    int nodeIndex = nodePos.y * mapSize.x + nodePos.x;
    float2 cellOffset = pos - nodePos;
    
    // Read the uint heights and convert them to floats for calculation.
    float4 heights = asfloat(TileBuffer[nodeIndex].VertexHeights);

    float height_x0_y0 = heights.x;
    float height_x1_y0 = heights.y;
    float height_x0_y1 = heights.z;
    float height_x1_y1 = heights.w;

    float height = lerp(lerp(height_x0_y0, height_x1_y0, cellOffset.x),
                         lerp(height_x0_y1, height_x1_y1, cellOffset.x),
                         cellOffset.y);
            
    float gradientX = lerp(height_x1_y0 - height_x0_y0, height_x1_y1 - height_x0_y1, cellOffset.y);
    float gradientY = lerp(height_x0_y1 - height_x0_y0, height_x1_y1 - height_x1_y0, cellOffset.x);

    return float4(height, gradientX, gradientY, 0);
}


[numthreads(64,1,1)]
void CSMain (uint3 id : SV_DispatchThreadID)
{
    uint index = id.x;
    // Fix signed/unsigned mismatch warning by casting numIterations to uint.
    if (index >= (uint)numIterations)
    {
        return;
    }

    srand(seed + index);
    
    float2 pos = rand_float2(float2(0,0), float2(mapSize.x - 1, mapSize.y - 1));
    float2 dir = float2(0, 0);
    float speed = initialSpeed;
    float water = initialWaterVolume;
    float sediment = 0;

    for (int lifetime = 0; lifetime < maxDropletLifetime; lifetime++)
    {
        int2 nodePos = int2(pos);
        
        float4 heightAndGradient = CalculateHeightAndGradient(pos);
        float currentHeight = heightAndGradient.x;
        float2 gradient = heightAndGradient.yz;
 
        dir = normalize(dir * inertia - gradient);
        
        float4 nextHeightAndGradient = CalculateHeightAndGradient(pos + dir);
        float newHeight = nextHeightAndGradient.x;
        
        speed = sqrt(speed * speed + max(0, currentHeight - newHeight) * gravity);
        
        pos += dir;

        if (pos.x <= 0 || pos.x >= mapSize.x - 1 || pos.y <= 0 || pos.y >= mapSize.y - 1 || speed == 0)
        {
            break;
        }

        float sedimentCapacity = max(abs(dot(normalize(gradient), dir)), minSedimentCapacity) * speed * water * sedimentCapacityFactor;
        
        float sedimentToDeposit = (sediment - sedimentCapacity) * depositSpeed;
        float sedimentToErode = (sedimentCapacity - sediment) * erodeSpeed;

        if (sediment > sedimentCapacity) { // Deposit
            sedimentToDeposit = min(sedimentToDeposit, sediment);
            sediment -= sedimentToDeposit;

            int tileIndex = nodePos.y * mapSize.x + nodePos.x;

            // With VertexHeights as uint, we can now use InterlockedAdd directly.
            // We still need to convert the float sediment value to its uint bit representation.
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.x, asuint(sedimentToDeposit * (1 - frac(pos.x)) * (1 - frac(pos.y))));
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.y, asuint(sedimentToDeposit * frac(pos.x) * (1 - frac(pos.y))));
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.z, asuint(sedimentToDeposit * (1 - frac(pos.x)) * frac(pos.y)));
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.w, asuint(sedimentToDeposit * frac(pos.x) * frac(pos.y)));

        } else { // Erode
            sedimentToErode = min(sedimentToErode, currentHeight);
            sediment += sedimentToErode;
            
            int tileIndex = nodePos.y * mapSize.x + nodePos.x;
            
            // The value to add is negative, so we must be careful with the asuint conversion.
            // But since addition/subtraction are just bitwise operations, it works correctly.
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.x, asuint(-sedimentToErode * (1 - frac(pos.x)) * (1 - frac(pos.y))));
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.y, asuint(-sedimentToErode * frac(pos.x) * (1 - frac(pos.y))));
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.z, asuint(-sedimentToErode * (1 - frac(pos.x)) * frac(pos.y)));
            InterlockedAdd(TileBuffer[tileIndex].VertexHeights.w, asuint(-sedimentToErode * frac(pos.x) * frac(pos.y)));
        }

        water *= (1 - evaporateSpeed);
    }
}